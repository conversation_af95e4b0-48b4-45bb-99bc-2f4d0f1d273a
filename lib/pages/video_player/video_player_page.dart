import 'package:my_video/app_imports.dart';

class VideoPlayerPage extends StatelessWidget {
  final MovieModel movie;

  const VideoPlayerPage({
    super.key,
    required this.movie,
  });

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    return GetBuilder<VideoPlayerController>(
      init: VideoPlayerController(movie),
      builder: (controller) {
        return Scaffold(
          backgroundColor: AppColorConstants.backgroundColor,
          body: Safe<PERSON><PERSON>(
            child: Column(
              children: [
                // Video Player Section
                _buildVideoPlayer(controller),

                // Content Section
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Movie Info
                        _buildMovieInfo(controller),

                        // Action Buttons
                        _buildActionButtons(controller),

                        // Related Videos
                        _buildRelatedVideos(controller),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildVideoPlayer(VideoPlayerController controller) {
    return Container(
      width: double.infinity,
      height: controller.isFullScreen ? MySize.height(812) : MySize.height(220),
      color: Colors.black,
      child: Stack(
        children: [
          // YouTube Player
          if (controller.youtubeController != null)
            YoutubePlayer(
              controller: controller.youtubeController!,
              aspectRatio: 16 / 9,
            ),

          // Custom Controls Overlay
          if (!controller.isFullScreen)
            Positioned(
              top: MySize.height(8),
              left: MySize.width(8),
              child: GestureDetector(
                onTap: () => Get.back(),
                child: Container(
                  padding: EdgeInsets.all(MySize.height(8)),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: AppColorConstants.textPrimary,
                    size: MySize.height(20),
                  ),
                ),
              ),
            ),

          // Fullscreen Toggle
          if (!controller.isFullScreen)
            Positioned(
              top: MySize.height(8),
              right: MySize.width(8),
              child: GestureDetector(
                onTap: controller.toggleFullScreen,
                child: Container(
                  padding: EdgeInsets.all(MySize.height(8)),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.fullscreen,
                    color: AppColorConstants.textPrimary,
                    size: MySize.height(20),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMovieInfo(VideoPlayerController controller) {
    return Padding(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            text: movie.title,
            fontSize: MySize.fontSize(20),
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(8),
          Row(
            children: [
              if (movie.rating != null) ...[
                Icon(
                  Icons.star,
                  size: MySize.height(16),
                  color: Colors.amber,
                ),
                Space.width(4),
                AppText(
                  text: movie.formattedRating,
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
                Space.width(16),
              ],
              if (movie.duration != null) ...[
                Icon(
                  Icons.access_time,
                  size: MySize.height(16),
                  color: AppColorConstants.textSecondary,
                ),
                Space.width(4),
                AppText(
                  text: movie.formattedDuration,
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
                Space.width(16),
              ],
              if (movie.releaseYear != null) ...[
                AppText(
                  text: movie.releaseYear.toString(),
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.textSecondary,
                ),
              ],
            ],
          ),
          if (movie.genre != null && movie.genre!.isNotEmpty) ...[
            Space.height(8),
            AppText(
              text: movie.genreString,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.primaryColor,
            ),
          ],
          if (movie.description != null) ...[
            Space.height(12),
            AppText(
              text: movie.description!,
              fontSize: MySize.fontSize(14),
              color: AppColorConstants.textSecondary,
              maxLines: controller.isDescriptionExpanded ? null : 3,
              overflow: controller.isDescriptionExpanded
                  ? null
                  : TextOverflow.ellipsis,
            ),
            Space.height(8),
            GestureDetector(
              onTap: controller.toggleDescription,
              child: AppText(
                text: controller.isDescriptionExpanded
                    ? 'Show Less'
                    : 'Show More',
                fontSize: MySize.fontSize(14),
                color: AppColorConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(VideoPlayerController controller) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      child: Row(
        children: [
          Expanded(
            child: AppButton(
              text: controller.isInPlaylist
                  ? 'Remove from Playlist'
                  : 'Add to Playlist',
              onPressed: controller.togglePlaylist,
              backgroundColor: controller.isInPlaylist
                  ? AppColorConstants.colorRed
                  : AppColorConstants.primaryColor,
              icon: Icon(
                controller.isInPlaylist ? Icons.remove : Icons.add,
                color: AppColorConstants.textPrimary,
              ),
            ),
          ),
          Space.width(12),
          AppIconButton(
            icon: Icons.share,
            onPressed: controller.shareMovie,
            backgroundColor: AppColorConstants.cardColor,
            color: AppColorConstants.textPrimary,
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedVideos(VideoPlayerController controller) {
    if (controller.relatedMovies.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Space.height(24),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: AppText(
            text: 'Related Videos',
            fontSize: MySize.fontSize(18),
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: controller.relatedMovies.length,
            itemBuilder: (context, index) {
              final relatedMovie = controller.relatedMovies[index];
              return MovieCard(
                movie: relatedMovie,
                onTap: () => controller.playRelatedMovie(relatedMovie),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
        Space.height(100), // Bottom padding
      ],
    );
  }
}
