import 'package:my_video/app_imports.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize shared preferences
      await AppSharedPreference.init();

      // Check connectivity
      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        await Future.delayed(AppDurationConstants.splashDuration);
        if (mounted) {
          context.go(AppRoutes.noInternet);
        }
        return;
      }

      // Wait for splash duration
      await Future.delayed(AppDurationConstants.splashDuration);

      if (mounted) {
        // Check if user is logged in
        final isLoggedIn = AppSharedPreference.isLoggedIn();

        if (isLoggedIn) {
          // Navigate to home/dashboard
          context.go(AppRoutes.home);
        } else {
          // Navigate to example page to showcase new components
          context.go(AppRoutes.example);
        }
      }
    } catch (e) {
      AppHelper.logError('Splash initialization error', e);
      if (mounted) {
        context.go(AppRoutes.login);
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: AppColorConstants.colorPrimary,
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App Logo
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColorConstants.colorWhite,
                        borderRadius:
                            BorderRadius.circular(AppSizeConstants.radiusLarge),
                        boxShadow: [
                          BoxShadow(
                            color:
                                AppColorConstants.colorBlack.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.flutter_dash,
                        size: 60,
                        color: AppColorConstants.colorPrimary,
                      ),
                    ),

                    const SizedBox(height: AppSizeConstants.marginLarge),

                    // App Name
                    const AppText(
                      text:  'Test Project',
                      color: AppColorConstants.colorWhite,
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: AppSizeConstants.marginSmall),

                    // App Tagline
                    AppText(
                      text:  'Your Flutter Project Template',
                      color: AppColorConstants.colorWhite.withOpacity(0.8),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: AppSizeConstants.marginExtraLarge),

                    // Loading Indicator
                    const AppLoader(
                      color: AppColorConstants.colorWhite,
                      size: 24,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
