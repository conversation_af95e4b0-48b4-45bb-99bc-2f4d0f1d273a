import 'package:my_video/app/ui/typography.dart';
import 'package:my_video/app_imports.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthenticationController>(
      init: AuthenticationController(),
      builder: (controller) {
        return AppScaffold(
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(AppSizeConstants.paddingLarge),
              child: Form(
                key: controller.loginFormKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Spacer(),

                    Column(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: AppColorConstants.colorPrimary,
                            borderRadius: BorderRadius.circular(AppSizeConstants.radiusLarge),
                          ),
                          child: const Icon(
                            Icons.flutter_dash,
                            size: 40,
                            color: AppColorConstants.colorWhite,
                          ),
                        ),
                        
                        const SizedBox(height: AppSizeConstants.marginLarge),
                        
                        const AppText(
                         text:  'welcome',
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: AppSizeConstants.marginSmall),
                        
                        const AppText(
                         text:  'Sign in to continue',
                          color: AppColorConstants.colorGrey,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizeConstants.marginExtraLarge),
                    
                    // Email Field
                    AppTextFormField(
                      controller: controller.emailController,
                      labelText: 'email',
                      hintText: 'Enter your email',
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      prefixIcon: const Icon(Icons.email_outlined),
                      validator: controller.validateEmail,
                    ),
                    
                    const SizedBox(height: AppSizeConstants.marginMedium),
                    
                    // Password Field
                    Obx(() => AppTextFormField(
                      controller: controller.passwordController,
                      labelText: 'password',
                      hintText: 'Enter your password',
                      obscureText: true,
                      textInputAction: TextInputAction.done,
                      prefixIcon: const Icon(Icons.lock_outlined),
                      validator: controller.validatePassword,
                      onChanged: (value) {
                        // Handle password change if needed
                      },
                    )),
                    
                    const SizedBox(height: AppSizeConstants.marginSmall),
                    
                    // Forgot Password
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          // Navigate to forgot password
                        },
                        child: const AppText(
                          text: 'forgot_password',
                          color: AppColorConstants.colorPrimary,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizeConstants.marginLarge),
                    
                    // Login Button
                    Obx(() => AppButton.primary(
                      text: 'sign_in',
                      onPressed: controller.apiStatus.isLoading ? null : controller.login,
                      isLoading: controller.apiStatus.isLoading,
                    )),
                    
                    const SizedBox(height: AppSizeConstants.marginMedium),
                    
                    // Sign Up Link
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const AppText(
                         text:  "Don't have an account? ",
                          color: AppColorConstants.colorGrey,
                        ),
                        TextButton(
                          onPressed: () {
                            // Navigate to sign up
                          },
                          child: const AppText(
                            text: 'sign_up',
                            color: AppColorConstants.colorPrimary,
                          ),
                        ),
                      ],
                    ),
                    
                    const Spacer(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
