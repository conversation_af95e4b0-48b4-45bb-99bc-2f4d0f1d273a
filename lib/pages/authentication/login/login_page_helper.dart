import 'package:my_video/app_imports.dart';

class LoginPageHelper {


  static void navigateToForgotPassword() {
    // Navigate to forgot password page
    // Get.toNamed(AppRoutes.forgotPassword);
  }

  static void navigateToSignUp() {
    // Navigate to sign up page
    // Get.toNamed(AppRoutes.signUp);
  }

  static void navigateToHome() {
    // Navigate to home page after successful login
    // Get.offAllNamed(AppRoutes.home);
  }

  static bool validateLoginForm(GlobalKey<FormState> formKey) {
    return formKey.currentState?.validate() ?? false;
  }

  static void showLoginError(String message) {
    AppHelper.showToast(message, isError: true);
  }

  static void showLoginSuccess(String message) {
    AppHelper.showToast(message);
  }

  static void clearLoginForm(AuthenticationController controller) {
    controller.emailController.clear();
    controller.passwordController.clear();
  }

  static Map<String, String> getLoginData(AuthenticationController controller) {
    return {
      'email': controller.emailController.text.trim(),
      'password': controller.passwordController.text.trim(),
    };
  }

  static bool isValidLoginData(AuthenticationController controller) {
    final email = controller.emailController.text.trim();
    final password = controller.passwordController.text.trim();
    
    return email.isNotEmpty && 
           password.isNotEmpty && 
           ValidationHelper.isValidEmail(email) &&
           password.length >= 6;
  }
}
