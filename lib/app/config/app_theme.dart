import 'package:my_video/app_imports.dart';

class AppTheme {
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: AppColorConstants.primaryColor,
      scaffoldBackgroundColor: AppColorConstants.backgroundColor,
      colorScheme: const ColorScheme.dark(
        primary: AppColorConstants.primaryColor,
        secondary: AppColorConstants.blue225BFF,
        surface: AppColorConstants.surfaceColor,
        background: AppColorConstants.backgroundColor,
        onPrimary: AppColorConstants.textPrimary,
        onSecondary: AppColorConstants.textPrimary,
        onSurface: AppColorConstants.textPrimary,
        onBackground: AppColorConstants.textPrimary,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColorConstants.backgroundColor,
        foregroundColor: AppColorConstants.textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColorConstants.textPrimary,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      bottomNavigationBarTheme: const BottomNavigationBarTheme(
        backgroundColor: AppColorConstants.surfaceColor,
        selectedItemColor: AppColorConstants.primaryColor,
        unselectedItemColor: AppColorConstants.textSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      cardTheme: CardTheme(
        color: AppColorConstants.cardColor,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorConstants.primaryColor,
          foregroundColor: AppColorConstants.textPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColorConstants.primaryColor,
          side: const BorderSide(color: AppColorConstants.primaryColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColorConstants.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColorConstants.surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColorConstants.dividerColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColorConstants.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColorConstants.primaryColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColorConstants.colorRed),
        ),
        labelStyle: const TextStyle(color: AppColorConstants.textSecondary),
        hintStyle: const TextStyle(color: AppColorConstants.textHint),
      ),
      dividerTheme: const DividerThemeData(
        color: AppColorConstants.dividerColor,
        thickness: 1,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColorConstants.primaryColor,
        foregroundColor: AppColorConstants.textPrimary,
        elevation: 6,
      ),
      dialogTheme: DialogTheme(
        backgroundColor: AppColorConstants.surfaceColor,
        titleTextStyle: const TextStyle(
          color: AppColorConstants.textPrimary,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        contentTextStyle: const TextStyle(
          color: AppColorConstants.textSecondary,
          fontSize: 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      bottomSheetTheme: const BottomSheetThemeData(
        backgroundColor: AppColorConstants.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),
      tabBarTheme: const TabBarTheme(
        labelColor: AppColorConstants.primaryColor,
        unselectedLabelColor: AppColorConstants.textSecondary,
        indicatorColor: AppColorConstants.primaryColor,
      ),
    );
  }
}
