enum Flavor { dev, prod }

class AppConfig {
  static AppConfig? _instance;
  static AppConfig get instance => _instance!;

  final String appName;
  final Flavor flavor;

  AppConfig._internal({
    required this.appName,
    required this.flavor,
  });

  static void create({
    required String appName,
    required Flavor flavor,
  }) {
    _instance = AppConfig._internal(
      appName: appName,
      flavor: flavor,
    );
  }

  bool get isDev => flavor == Flavor.dev;
  bool get isProd => flavor == Flavor.prod;

  String get baseUrl {
    switch (flavor) {
      case Flavor.dev:
        return 'https://dev-api.example.com/';
      case Flavor.prod:
        return 'https://api.example.com/';
    }
  }
}
