import 'package:my_video/app_imports.dart';

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final double? elevation;
  final Size? minimumSize;
  final Widget? icon;
  final bool isLoading;
  final bool isOutlined;

  const AppButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.borderRadius,
    this.elevation,
    this.minimumSize,
    this.icon,
    this.isLoading = false,
    this.isOutlined = false,
  });

  const AppButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.borderRadius,
    this.elevation,
    this.minimumSize,
    this.icon,
    this.isLoading = false,
  })  : backgroundColor = AppColorConstants.colorPrimary,
        isOutlined = false;

  const AppButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.borderRadius,
    this.elevation,
    this.minimumSize,
    this.icon,
    this.isLoading = false,
  })  : backgroundColor = AppColorConstants.colorLightGrey,
        isOutlined = false;

  const AppButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.borderRadius,
    this.elevation,
    this.minimumSize,
    this.icon,
    this.isLoading = false,
  }) : isOutlined = true;

  @override
  Widget build(BuildContext context) {
    final buttonStyle = isOutlined
        ? OutlinedButton.styleFrom(
            foregroundColor: textColor ?? AppColorConstants.colorPrimary,
            backgroundColor:
                backgroundColor ?? AppColorConstants.colorTransparent,
            padding: padding ??
                const EdgeInsets.symmetric(
                  horizontal: AppSizeConstants.paddingLarge,
                  vertical: AppSizeConstants.paddingMedium,
                ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                borderRadius ?? AppSizeConstants.radiusMedium,
              ),
            ),
            minimumSize: minimumSize ?? const Size(double.infinity, 48),
            elevation: elevation ?? 0,
            side: BorderSide(
              color: textColor ?? AppColorConstants.colorPrimary,
              width: 1,
            ),
          )
        : ElevatedButton.styleFrom(
            foregroundColor: textColor ?? AppColorConstants.colorWhite,
            backgroundColor: backgroundColor ?? AppColorConstants.colorPrimary,
            padding: padding ??
                const EdgeInsets.symmetric(
                  horizontal: AppSizeConstants.paddingLarge,
                  vertical: AppSizeConstants.paddingMedium,
                ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                borderRadius ?? AppSizeConstants.radiusMedium,
              ),
            ),
            minimumSize: minimumSize ?? const Size(double.infinity, 48),
            elevation: elevation ?? AppSizeConstants.elevationSmall,
          );

    Widget buttonChild = isLoading
        ? SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                textColor ?? AppColorConstants.colorWhite,
              ),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) ...[
                icon!,
                const SizedBox(width: AppSizeConstants.paddingSmall),
              ],
              Text(
                text,
                style: TextStyle(
                  fontSize: fontSize ?? AppSizeConstants.fontSizeMedium,
                  fontWeight: fontWeight ?? FontWeight.w600,
                  fontFamily: AppAssetsConstants.defaultFont,
                ),
              ),
            ],
          );

    return isOutlined
        ? OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: buttonStyle,
            child: buttonChild,
          )
        : ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: buttonStyle,
            child: buttonChild,
          );
  }
}

class AppIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? color;
  final Color? backgroundColor;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;

  const AppIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.color,
    this.backgroundColor,
    this.size,
    this.padding,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: backgroundColor ?? AppColorConstants.colorTransparent,
      borderRadius: BorderRadius.circular(
        borderRadius ?? AppSizeConstants.radiusMedium,
      ),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(
          borderRadius ?? AppSizeConstants.radiusMedium,
        ),
        child: Padding(
          padding:
              padding ?? const EdgeInsets.all(AppSizeConstants.paddingSmall),
          child: Icon(
            icon,
            color: color ?? AppColorConstants.colorPrimary,
            size: size ?? 24,
          ),
        ),
      ),
    );
  }
}
