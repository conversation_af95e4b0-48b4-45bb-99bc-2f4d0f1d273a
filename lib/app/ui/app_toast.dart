import 'package:my_video/app_imports.dart';

class AppToast {
  static void showSuccess(String message) {
    BotToast.showText(
      text: message,
      textStyle: const TextStyle(
        color: AppColorConstants.colorWhite,
        fontSize: AppSizeConstants.fontSizeMedium,
      ),
      contentColor: AppColorConstants.colorGreen,
      duration: AppDurationConstants.toastDuration,
      borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
    );
  }

  static void showError(String message) {
    BotToast.showText(
      text: message,
      textStyle: const TextStyle(
        color: AppColorConstants.colorWhite,
        fontSize: AppSizeConstants.fontSizeMedium,
      ),
      contentColor: AppColorConstants.colorRed,
      duration: AppDurationConstants.toastDuration,
      borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
    );
  }

  static void showInfo(String message) {
    BotToast.showText(
      text: message,
      textStyle: const TextStyle(
        color: AppColorConstants.colorWhite,
        fontSize: AppSizeConstants.fontSizeMedium,
      ),
      contentColor: AppColorConstants.colorBlue,
      duration: AppDurationConstants.toastDuration,
      borderRadius: BorderRadius.circular(AppSizeConstants.radiusMedium),
    );
  }
}
