import 'package:my_video/app_imports.dart';

class AppSharedPreference {
  static SharedPreferences? _preferences;

  static Future<void> init() async {
    _preferences = await SharedPreferences.getInstance();
  }

  static SharedPreferences get _prefs {
    if (_preferences == null) {
      throw Exception(
          'SharedPreferences not initialized. Call AppSharedPreference.init() first.');
    }
    return _preferences!;
  }

  // String operations
  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  static String? getString(String key, {String? defaultValue}) {
    return _prefs.getString(key) ?? defaultValue;
  }

  // Int operations
  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  static int? getInt(String key, {int? defaultValue}) {
    return _prefs.getInt(key) ?? defaultValue;
  }

  // Double operations
  static Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key, {double? defaultValue}) {
    return _prefs.getDouble(key) ?? defaultValue;
  }

  // Bool operations
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  static bool? getBool(String key, {bool? defaultValue}) {
    return _prefs.getBool(key) ?? defaultValue;
  }

  // List<String> operations
  static Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  static List<String>? getStringList(String key, {List<String>? defaultValue}) {
    return _prefs.getStringList(key) ?? defaultValue;
  }

  // Object operations (JSON)
  static Future<bool> setObject(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  static Map<String, dynamic>? getObject(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      } catch (e) {
        AppHelper.logError('Error decoding JSON for key: $key', e);
        return null;
      }
    }
    return null;
  }

  // List<Object> operations (JSON)
  static Future<bool> setObjectList(
      String key, List<Map<String, dynamic>> value) async {
    final jsonString = jsonEncode(value);
    return await setString(key, jsonString);
  }

  static List<Map<String, dynamic>>? getObjectList(String key) {
    final jsonString = getString(key);
    if (jsonString != null) {
      try {
        final decoded = jsonDecode(jsonString) as List;
        return decoded.cast<Map<String, dynamic>>();
      } catch (e) {
        AppHelper.logError('Error decoding JSON list for key: $key', e);
        return null;
      }
    }
    return null;
  }

  // Remove operations
  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  static Future<bool> clear() async {
    return await _prefs.clear();
  }

  // Check if key exists
  static bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  // Get all keys
  static Set<String> getKeys() {
    return _prefs.getKeys();
  }

  // Common app-specific keys
  static const String keyIsFirstTime = 'is_first_time';
  static const String keyUserToken = 'user_token';
  static const String keyUserId = 'user_id';
  static const String keyUserData = 'user_data';
  static const String keyLanguage = 'language';
  static const String keyTheme = 'theme';
  static const String keyNotificationEnabled = 'notification_enabled';

  // App-specific helper methods
  static Future<bool> setUserToken(String token) async {
    return await setString(keyUserToken, token);
  }

  static String? getUserToken() {
    return getString(keyUserToken);
  }

  static Future<bool> setUserId(String userId) async {
    return await setString(keyUserId, userId);
  }

  static String? getUserId() {
    return getString(keyUserId);
  }

  static Future<bool> setUserData(Map<String, dynamic> userData) async {
    return await setObject(keyUserData, userData);
  }

  static Map<String, dynamic>? getUserData() {
    return getObject(keyUserData);
  }

  static Future<bool> setIsFirstTime(bool isFirstTime) async {
    return await setBool(keyIsFirstTime, isFirstTime);
  }

  static bool getIsFirstTime() {
    return getBool(keyIsFirstTime, defaultValue: true) ?? true;
  }

  static Future<bool> setLanguage(String language) async {
    return await setString(keyLanguage, language);
  }

  static String getLanguage() {
    return getString(keyLanguage, defaultValue: 'en') ?? 'en';
  }

  static Future<bool> setNotificationEnabled(bool enabled) async {
    return await setBool(keyNotificationEnabled, enabled);
  }

  static bool getNotificationEnabled() {
    return getBool(keyNotificationEnabled, defaultValue: true) ?? true;
  }

  static Future<bool> logout() async {
    await remove(keyUserToken);
    await remove(keyUserId);
    await remove(keyUserData);
    return true;
  }

  static bool isLoggedIn() {
    final token = getUserToken();
    return token != null && token.isNotEmpty;
  }
}
