import 'package:my_video/app_imports.dart';

class DownloadHelper {
  static Future<String?> getDownloadPath() async {
    try {
      if (Platform.isAndroid) {
        return await ExternalPath.getExternalStoragePublicDirectory(
          ExternalPath.DIRECTORY_DOWNLOADS,
        );
      } else if (Platform.isIOS) {
        final directory = await getApplicationDocumentsDirectory();
        return directory.path;
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error getting download path', e);
      return null;
    }
  }

  static Future<File?> downloadFile({
    required String url,
    required String fileName,
    String? customPath,
    Function(int, int)? onProgress,
  }) async {
    try {
      final downloadPath = customPath ?? await getDownloadPath();
      if (downloadPath == null) {
        AppHelper.showToast('Unable to get download path', isError: true);
        return null;
      }

      final filePath = '$downloadPath/$fileName';
      final file = File(filePath);

      // Create directory if it doesn't exist
      await file.parent.create(recursive: true);

      final client = Client();
      final request = Request('GET', Uri.parse(url));
      final response = await client.send(request);

      if (response.statusCode == 200) {
        final contentLength = response.contentLength ?? 0;
        int downloadedBytes = 0;

        final sink = file.openWrite();
        
        await response.stream.listen(
          (chunk) {
            sink.add(chunk);
            downloadedBytes += chunk.length;
            onProgress?.call(downloadedBytes, contentLength);
          },
          onDone: () async {
            await sink.close();
            client.close();
          },
          onError: (error) async {
            await sink.close();
            client.close();
            throw error;
          },
        ).asFuture();

        AppHelper.showToast('File downloaded successfully');
        return file;
      } else {
        client.close();
        AppHelper.showToast('Failed to download file', isError: true);
        return null;
      }
    } catch (e) {
      AppHelper.logError('Error downloading file', e);
      AppHelper.showToast('Error downloading file', isError: true);
      return null;
    }
  }

  static Future<bool> saveFileToDownloads({
    required File sourceFile,
    required String fileName,
  }) async {
    try {
      final downloadPath = await getDownloadPath();
      if (downloadPath == null) {
        AppHelper.showToast('Unable to get download path', isError: true);
        return false;
      }

      final destinationPath = '$downloadPath/$fileName';
      final destinationFile = File(destinationPath);

      // Create directory if it doesn't exist
      await destinationFile.parent.create(recursive: true);

      // Copy file to downloads
      await sourceFile.copy(destinationPath);

      AppHelper.showToast('File saved to downloads');
      return true;
    } catch (e) {
      AppHelper.logError('Error saving file to downloads', e);
      AppHelper.showToast('Error saving file', isError: true);
      return false;
    }
  }

  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      AppHelper.logError('Error deleting file', e);
      return false;
    }
  }

  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      AppHelper.logError('Error checking file existence', e);
      return false;
    }
  }

  static Future<int?> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return null;
    } catch (e) {
      AppHelper.logError('Error getting file size', e);
      return null;
    }
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
