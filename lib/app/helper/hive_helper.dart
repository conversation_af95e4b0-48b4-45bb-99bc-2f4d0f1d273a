import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_video/app_imports.dart';

class HiveHelper {
  static const String _movieBoxName = 'movies';
  static const String _categoryBoxName = 'categories';
  static const String _playlistBoxName = 'playlists';
  static const String _settingsBoxName = 'settings';

  static Box<MovieModel>? _movieBox;
  static Box<CategoryModel>? _categoryBox;
  static Box<PlaylistModel>? _playlistBox;
  static Box<dynamic>? _settingsBox;

  // Initialize Hive database
  static Future<void> init() async {
    try {
      // Initialize Hive
      await Hive.initFlutter();

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(MovieModelAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(CategoryModelAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(PlaylistModelAdapter());
      }

      // Open boxes
      _movieBox = await Hive.openBox<MovieModel>(_movieBoxName);
      _categoryBox = await Hive.openBox<CategoryModel>(_categoryBoxName);
      _playlistBox = await Hive.openBox<PlaylistModel>(_playlistBoxName);
      _settingsBox = await Hive.openBox(_settingsBoxName);

      // Create default playlist if it doesn't exist
      await _createDefaultPlaylistIfNeeded();

      Logger().i('Hive database initialized successfully');
    } catch (e) {
      Logger().e('Error initializing Hive database: $e');
      rethrow;
    }
  }

  // Create default playlist if it doesn't exist
  static Future<void> _createDefaultPlaylistIfNeeded() async {
    if (_playlistBox != null && _playlistBox!.isEmpty) {
      final defaultPlaylist = PlaylistModel.createDefault();
      await _playlistBox!.put(defaultPlaylist.id, defaultPlaylist);
      Logger().i('Default playlist created');
    }
  }

  // Movie operations
  static Box<MovieModel> get movieBox {
    if (_movieBox == null || !_movieBox!.isOpen) {
      throw Exception('Movie box is not initialized or closed');
    }
    return _movieBox!;
  }

  static Future<void> saveMovie(MovieModel movie) async {
    await movieBox.put(movie.id, movie);
  }

  static Future<void> saveMovies(List<MovieModel> movies) async {
    final Map<String, MovieModel> movieMap = {
      for (var movie in movies) movie.id: movie
    };
    await movieBox.putAll(movieMap);
  }

  static MovieModel? getMovie(String id) {
    return movieBox.get(id);
  }

  static List<MovieModel> getAllMovies() {
    return movieBox.values.toList();
  }

  static List<MovieModel> getMoviesByCategory(String category) {
    return movieBox.values
        .where((movie) => movie.category.toLowerCase() == category.toLowerCase())
        .toList();
  }

  static List<MovieModel> getFeaturedMovies() {
    return movieBox.values.where((movie) => movie.isFeatured).toList();
  }

  static Future<void> deleteMovie(String id) async {
    await movieBox.delete(id);
  }

  static Future<void> clearMovies() async {
    await movieBox.clear();
  }

  // Category operations
  static Box<CategoryModel> get categoryBox {
    if (_categoryBox == null || !_categoryBox!.isOpen) {
      throw Exception('Category box is not initialized or closed');
    }
    return _categoryBox!;
  }

  static Future<void> saveCategory(CategoryModel category) async {
    await categoryBox.put(category.id, category);
  }

  static Future<void> saveCategories(List<CategoryModel> categories) async {
    final Map<String, CategoryModel> categoryMap = {
      for (var category in categories) category.id: category
    };
    await categoryBox.putAll(categoryMap);
  }

  static CategoryModel? getCategory(String id) {
    return categoryBox.get(id);
  }

  static List<CategoryModel> getAllCategories() {
    return categoryBox.values.toList()
      ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
  }

  static List<CategoryModel> getActiveCategories() {
    return categoryBox.values
        .where((category) => category.isActive)
        .toList()
      ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
  }

  static Future<void> deleteCategory(String id) async {
    await categoryBox.delete(id);
  }

  static Future<void> clearCategories() async {
    await categoryBox.clear();
  }

  // Playlist operations
  static Box<PlaylistModel> get playlistBox {
    if (_playlistBox == null || !_playlistBox!.isOpen) {
      throw Exception('Playlist box is not initialized or closed');
    }
    return _playlistBox!;
  }

  static Future<void> savePlaylist(PlaylistModel playlist) async {
    await playlistBox.put(playlist.id, playlist);
  }

  static PlaylistModel? getPlaylist(String id) {
    return playlistBox.get(id);
  }

  static PlaylistModel? getDefaultPlaylist() {
    return playlistBox.values.firstWhere(
      (playlist) => playlist.isDefault,
      orElse: () => PlaylistModel.createDefault(),
    );
  }

  static List<PlaylistModel> getAllPlaylists() {
    return playlistBox.values.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  static Future<void> addMovieToPlaylist(String playlistId, String movieId) async {
    final playlist = getPlaylist(playlistId);
    if (playlist != null) {
      final updatedPlaylist = playlist.addMovie(movieId);
      await savePlaylist(updatedPlaylist);
    }
  }

  static Future<void> removeMovieFromPlaylist(String playlistId, String movieId) async {
    final playlist = getPlaylist(playlistId);
    if (playlist != null) {
      final updatedPlaylist = playlist.removeMovie(movieId);
      await savePlaylist(updatedPlaylist);
    }
  }

  static Future<void> addMovieToDefaultPlaylist(String movieId) async {
    const defaultPlaylistId = 'default_playlist';
    await addMovieToPlaylist(defaultPlaylistId, movieId);
  }

  static Future<void> removeMovieFromDefaultPlaylist(String movieId) async {
    const defaultPlaylistId = 'default_playlist';
    await removeMovieFromPlaylist(defaultPlaylistId, movieId);
  }

  static bool isMovieInDefaultPlaylist(String movieId) {
    final defaultPlaylist = getDefaultPlaylist();
    return defaultPlaylist?.containsMovie(movieId) ?? false;
  }

  static List<MovieModel> getPlaylistMovies(String playlistId) {
    final playlist = getPlaylist(playlistId);
    if (playlist == null) return [];

    return playlist.movieIds
        .map((movieId) => getMovie(movieId))
        .where((movie) => movie != null)
        .cast<MovieModel>()
        .toList();
  }

  static Future<void> deletePlaylist(String id) async {
    await playlistBox.delete(id);
  }

  // Settings operations
  static Box get settingsBox {
    if (_settingsBox == null || !_settingsBox!.isOpen) {
      throw Exception('Settings box is not initialized or closed');
    }
    return _settingsBox!;
  }

  static Future<void> saveSetting(String key, dynamic value) async {
    await settingsBox.put(key, value);
  }

  static T? getSetting<T>(String key, {T? defaultValue}) {
    return settingsBox.get(key, defaultValue: defaultValue) as T?;
  }

  static Future<void> deleteSetting(String key) async {
    await settingsBox.delete(key);
  }

  static Future<void> clearSettings() async {
    await settingsBox.clear();
  }

  // Utility methods
  static Future<void> clearAllData() async {
    await clearMovies();
    await clearCategories();
    await playlistBox.clear();
    await clearSettings();
    
    // Recreate default playlist
    await _createDefaultPlaylistIfNeeded();
  }

  static Future<void> closeBoxes() async {
    await _movieBox?.close();
    await _categoryBox?.close();
    await _playlistBox?.close();
    await _settingsBox?.close();
  }

  // Database info
  static Map<String, int> getDatabaseInfo() {
    return {
      'movies': movieBox.length,
      'categories': categoryBox.length,
      'playlists': playlistBox.length,
      'settings': settingsBox.length,
    };
  }
}
