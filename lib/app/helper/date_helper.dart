import 'package:my_video/app_imports.dart';

class DateHelper {
  static const String defaultDateFormat = 'dd/MM/yyyy';
  static const String defaultTimeFormat = 'HH:mm';
  static const String defaultDateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-dd';
  static const String apiDateTimeFormat = 'yyyy-MM-ddTHH:mm:ss.SSSZ';

  static String formatDate(DateTime? date, {String format = defaultDateFormat}) {
    if (date == null) return '';
    return DateFormat(format).format(date);
  }

  static String formatTime(DateTime? time, {String format = defaultTimeFormat}) {
    if (time == null) return '';
    return DateFormat(format).format(time);
  }

  static String formatDateTime(DateTime? dateTime, {String format = defaultDateTimeFormat}) {
    if (dateTime == null) return '';
    return DateFormat(format).format(dateTime);
  }

  static DateTime? parseDate(String? dateString, {String format = defaultDateFormat}) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return DateFormat(format).parse(dateString);
    } catch (e) {
      AppHelper.logError('Error parsing date: $dateString', e);
      return null;
    }
  }

  static DateTime? parseDateTime(String? dateTimeString, {String format = defaultDateTimeFormat}) {
    if (dateTimeString == null || dateTimeString.isEmpty) return null;
    try {
      return DateFormat(format).parse(dateTimeString);
    } catch (e) {
      AppHelper.logError('Error parsing datetime: $dateTimeString', e);
      return null;
    }
  }

  static String formatForApi(DateTime? date) {
    if (date == null) return '';
    return DateFormat(apiDateFormat).format(date);
  }

  static String formatDateTimeForApi(DateTime? dateTime) {
    if (dateTime == null) return '';
    return DateFormat(apiDateTimeFormat).format(dateTime);
  }

  static DateTime? parseFromApi(String? apiDateString) {
    if (apiDateString == null || apiDateString.isEmpty) return null;
    try {
      return DateFormat(apiDateFormat).parse(apiDateString);
    } catch (e) {
      AppHelper.logError('Error parsing API date: $apiDateString', e);
      return null;
    }
  }

  static DateTime? parseDateTimeFromApi(String? apiDateTimeString) {
    if (apiDateTimeString == null || apiDateTimeString.isEmpty) return null;
    try {
      return DateFormat(apiDateTimeFormat).parse(apiDateTimeString);
    } catch (e) {
      AppHelper.logError('Error parsing API datetime: $apiDateTimeString', e);
      return null;
    }
  }

  static String getTimeAgo(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return '${years}y ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '${months}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  static bool isSameDay(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) return false;
    return date1.year == date2.year && 
           date1.month == date2.month && 
           date1.day == date2.day;
  }

  static bool isToday(DateTime? date) {
    if (date == null) return false;
    return isSameDay(date, DateTime.now());
  }

  static bool isYesterday(DateTime? date) {
    if (date == null) return false;
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  static List<DateTime> getDaysInMonth(int year, int month) {
    final firstDay = DateTime(year, month, 1);
    final lastDay = DateTime(year, month + 1, 0);
    final days = <DateTime>[];
    
    for (int i = 0; i < lastDay.day; i++) {
      days.add(firstDay.add(Duration(days: i)));
    }
    
    return days;
  }

  static int getDaysInMonthCount(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  static String getMonthName(int month) {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month - 1];
  }

  static String getDayName(int weekday) {
    const dayNames = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    ];
    return dayNames[weekday - 1];
  }
}
