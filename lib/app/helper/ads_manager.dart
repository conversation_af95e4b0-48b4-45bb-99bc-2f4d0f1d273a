import 'package:my_video/app_imports.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdsManager {
  static final Logger _logger = Logger();
  static bool _isInitialized = false;
  static bool _isPremiumUser = false;

  // Ad Unit IDs (Test IDs - Replace with real ones in production)
  static const String _bannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
  static const String _interstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';
  static const String _nativeAdUnitId = 'ca-app-pub-3940256099942544/2247696110';

  // Ad instances
  static BannerAd? _bannerAd;
  static InterstitialAd? _interstitialAd;
  static NativeAd? _nativeAd;

  // Ad loading states
  static bool _isBannerAdLoaded = false;
  static bool _isInterstitialAdLoaded = false;
  static bool _isNativeAdLoaded = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await MobileAds.instance.initialize();
      _isInitialized = true;
      _logger.i('Google Mobile Ads initialized successfully');
      
      // Check premium status
      _updatePremiumStatus();
      
      // Pre-load ads if not premium
      if (!_isPremiumUser) {
        await _preloadAds();
      }
    } catch (e) {
      _logger.e('Error initializing Google Mobile Ads: $e');
    }
  }

  static void _updatePremiumStatus() {
    _isPremiumUser = HiveHelper.getSetting<bool>('is_premium_user', defaultValue: false) ?? false;
    _logger.i('Premium status: $_isPremiumUser');
  }

  static Future<void> _preloadAds() async {
    await Future.wait([
      _loadBannerAd(),
      _loadInterstitialAd(),
      _loadNativeAd(),
    ]);
  }

  // Banner Ad Methods
  static Future<void> _loadBannerAd() async {
    if (_isPremiumUser) return;

    try {
      _bannerAd = BannerAd(
        adUnitId: _bannerAdUnitId,
        size: AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            _isBannerAdLoaded = true;
            _logger.i('Banner ad loaded successfully');
          },
          onAdFailedToLoad: (ad, error) {
            _isBannerAdLoaded = false;
            ad.dispose();
            _logger.e('Banner ad failed to load: $error');
          },
          onAdOpened: (ad) => _logger.i('Banner ad opened'),
          onAdClosed: (ad) => _logger.i('Banner ad closed'),
        ),
      );

      await _bannerAd!.load();
    } catch (e) {
      _logger.e('Error loading banner ad: $e');
    }
  }

  static Widget? getBannerAdWidget() {
    if (_isPremiumUser || !_isBannerAdLoaded || _bannerAd == null) {
      return null;
    }

    return Container(
      width: _bannerAd!.size.width.toDouble(),
      height: _bannerAd!.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd!),
    );
  }

  // Interstitial Ad Methods
  static Future<void> _loadInterstitialAd() async {
    if (_isPremiumUser) return;

    try {
      await InterstitialAd.load(
        adUnitId: _interstitialAdUnitId,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _interstitialAd = ad;
            _isInterstitialAdLoaded = true;
            _logger.i('Interstitial ad loaded successfully');

            ad.fullScreenContentCallback = FullScreenContentCallback(
              onAdShowedFullScreenContent: (ad) => _logger.i('Interstitial ad showed'),
              onAdDismissedFullScreenContent: (ad) {
                ad.dispose();
                _isInterstitialAdLoaded = false;
                _loadInterstitialAd(); // Preload next ad
                _logger.i('Interstitial ad dismissed');
              },
              onAdFailedToShowFullScreenContent: (ad, error) {
                ad.dispose();
                _isInterstitialAdLoaded = false;
                _logger.e('Interstitial ad failed to show: $error');
              },
            );
          },
          onAdFailedToLoad: (error) {
            _isInterstitialAdLoaded = false;
            _logger.e('Interstitial ad failed to load: $error');
          },
        ),
      );
    } catch (e) {
      _logger.e('Error loading interstitial ad: $e');
    }
  }

  static Future<void> showInterstitialAd({VoidCallback? onAdClosed}) async {
    if (_isPremiumUser) {
      onAdClosed?.call();
      return;
    }

    if (_isInterstitialAdLoaded && _interstitialAd != null) {
      try {
        await _interstitialAd!.show();
        onAdClosed?.call();
      } catch (e) {
        _logger.e('Error showing interstitial ad: $e');
        onAdClosed?.call();
      }
    } else {
      _logger.w('Interstitial ad not ready');
      onAdClosed?.call();
    }
  }

  // Native Ad Methods
  static Future<void> _loadNativeAd() async {
    if (_isPremiumUser) return;

    try {
      _nativeAd = NativeAd(
        adUnitId: _nativeAdUnitId,
        request: const AdRequest(),
        listener: NativeAdListener(
          onAdLoaded: (ad) {
            _isNativeAdLoaded = true;
            _logger.i('Native ad loaded successfully');
          },
          onAdFailedToLoad: (ad, error) {
            _isNativeAdLoaded = false;
            ad.dispose();
            _logger.e('Native ad failed to load: $error');
          },
          onAdOpened: (ad) => _logger.i('Native ad opened'),
          onAdClosed: (ad) => _logger.i('Native ad closed'),
        ),
        nativeTemplateStyle: NativeTemplateStyle(
          templateType: TemplateType.medium,
          mainBackgroundColor: AppColorConstants.cardColor,
          cornerRadius: MySize.radius(12),
          callToActionTextStyle: NativeTemplateTextStyle(
            textColor: AppColorConstants.textPrimary,
            backgroundColor: AppColorConstants.primaryColor,
            style: NativeTemplateFontStyle.bold,
            size: MySize.fontSize(14),
          ),
          primaryTextStyle: NativeTemplateTextStyle(
            textColor: AppColorConstants.textPrimary,
            style: NativeTemplateFontStyle.bold,
            size: MySize.fontSize(16),
          ),
          secondaryTextStyle: NativeTemplateTextStyle(
            textColor: AppColorConstants.textSecondary,
            style: NativeTemplateFontStyle.normal,
            size: MySize.fontSize(14),
          ),
        ),
      );

      await _nativeAd!.load();
    } catch (e) {
      _logger.e('Error loading native ad: $e');
    }
  }

  static Widget? getNativeAdWidget() {
    if (_isPremiumUser || !_isNativeAdLoaded || _nativeAd == null) {
      return null;
    }

    return Container(
      height: MySize.height(200),
      margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
      child: AdWidget(ad: _nativeAd!),
    );
  }

  // Ad Placeholder Widgets
  static Widget buildAdPlaceholder({
    double? width,
    double? height,
    String? label,
  }) {
    if (_isPremiumUser) return const SizedBox.shrink();

    return Container(
      width: width ?? MySize.width(140),
      height: height ?? MySize.height(200),
      margin: EdgeInsets.only(right: MySize.width(12)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: AppColorConstants.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.ads_click_outlined,
            size: MySize.height(40),
            color: AppColorConstants.textHint,
          ),
          Space.height(8),
          AppText(
            text: label ?? 'Advertisement',
            fontSize: MySize.fontSize(12),
            color: AppColorConstants.textHint,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Utility Methods
  static bool get isPremiumUser => _isPremiumUser;
  static bool get shouldShowAds => !_isPremiumUser;

  static void updatePremiumStatus(bool isPremium) {
    _isPremiumUser = isPremium;
    _logger.i('Premium status updated: $_isPremiumUser');
    
    if (_isPremiumUser) {
      _disposeAllAds();
    } else {
      _preloadAds();
    }
  }

  static void _disposeAllAds() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _nativeAd?.dispose();
    
    _bannerAd = null;
    _interstitialAd = null;
    _nativeAd = null;
    
    _isBannerAdLoaded = false;
    _isInterstitialAdLoaded = false;
    _isNativeAdLoaded = false;
    
    _logger.i('All ads disposed');
  }

  static void dispose() {
    _disposeAllAds();
    _isInitialized = false;
  }
}
