import 'package:my_video/app_imports.dart';

class MySize {
  static late MediaQueryData _mediaQueryData;
  static late double screenWidth;
  static late double screenHeight;
  static late double blockSizeHorizontal;
  static late double blockSizeVertical;
  static late double safeAreaHorizontal;
  static late double safeAreaVertical;
  static late double safeBlockHorizontal;
  static late double safeBlockVertical;

  static void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;
    safeAreaHorizontal = _mediaQueryData.padding.left + _mediaQueryData.padding.right;
    safeAreaVertical = _mediaQueryData.padding.top + _mediaQueryData.padding.bottom;
    safeBlockHorizontal = (screenWidth - safeAreaHorizontal) / 100;
    safeBlockVertical = (screenHeight - safeAreaVertical) / 100;
  }

  static double height(double inputHeight) {
    return (inputHeight / 812.0) * screenHeight;
  }

  static double width(double inputWidth) {
    return (inputWidth / 375.0) * screenWidth;
  }

  static double fontSize(double inputFontSize) {
    return (inputFontSize / 375.0) * screenWidth;
  }

  static double radius(double inputRadius) {
    return (inputRadius / 375.0) * screenWidth;
  }

  static double get size4 => height(4);
  static double get size6 => height(6);
  static double get size8 => height(8);
  static double get size10 => height(10);
  static double get size12 => height(12);
  static double get size14 => height(14);
  static double get size16 => height(16);
  static double get size18 => height(18);
  static double get size20 => height(20);
  static double get size22 => height(22);
  static double get size24 => height(24);
  static double get size26 => height(26);
  static double get size28 => height(28);
  static double get size30 => height(30);
  static double get size32 => height(32);
  static double get size34 => height(34);
  static double get size36 => height(36);
  static double get size38 => height(38);
  static double get size40 => height(40);
  static double get size42 => height(42);
  static double get size44 => height(44);
  static double get size46 => height(46);
  static double get size48 => height(48);
  static double get size50 => height(50);
  static double get size52 => height(52);
  static double get size54 => height(54);
  static double get size56 => height(56);
  static double get size58 => height(58);
  static double get size60 => height(60);
  static double get size64 => height(64);
  static double get size68 => height(68);
  static double get size72 => height(72);
  static double get size76 => height(76);
  static double get size80 => height(80);
  static double get size84 => height(84);
  static double get size88 => height(88);
  static double get size92 => height(92);
  static double get size96 => height(96);
  static double get size100 => height(100);
  static double get size120 => height(120);
  static double get size140 => height(140);
  static double get size160 => height(160);
  static double get size180 => height(180);
  static double get size200 => height(200);
}

class Space {
  static Widget height(double height) {
    return SizedBox(height: MySize.height(height));
  }

  static Widget width(double width) {
    return SizedBox(width: MySize.width(width));
  }

  static Widget get h4 => height(4);
  static Widget get h6 => height(6);
  static Widget get h8 => height(8);
  static Widget get h10 => height(10);
  static Widget get h12 => height(12);
  static Widget get h14 => height(14);
  static Widget get h16 => height(16);
  static Widget get h18 => height(18);
  static Widget get h20 => height(20);
  static Widget get h24 => height(24);
  static Widget get h28 => height(28);
  static Widget get h32 => height(32);
  static Widget get h36 => height(36);
  static Widget get h40 => height(40);
  static Widget get h44 => height(44);
  static Widget get h48 => height(48);
  static Widget get h52 => height(52);
  static Widget get h56 => height(56);
  static Widget get h60 => height(60);
  static Widget get h64 => height(64);
  static Widget get h68 => height(68);
  static Widget get h72 => height(72);
  static Widget get h80 => height(80);
  static Widget get h88 => height(88);
  static Widget get h96 => height(96);
  static Widget get h100 => height(100);

  static Widget get w4 => width(4);
  static Widget get w6 => width(6);
  static Widget get w8 => width(8);
  static Widget get w10 => width(10);
  static Widget get w12 => width(12);
  static Widget get w14 => width(14);
  static Widget get w16 => width(16);
  static Widget get w18 => width(18);
  static Widget get w20 => width(20);
  static Widget get w24 => width(24);
  static Widget get w28 => width(28);
  static Widget get w32 => width(32);
  static Widget get w36 => width(36);
  static Widget get w40 => width(40);
  static Widget get w44 => width(44);
  static Widget get w48 => width(48);
  static Widget get w52 => width(52);
  static Widget get w56 => width(56);
  static Widget get w60 => width(60);
  static Widget get w64 => width(64);
  static Widget get w68 => width(68);
  static Widget get w72 => width(72);
  static Widget get w80 => width(80);
  static Widget get w88 => width(88);
  static Widget get w96 => width(96);
  static Widget get w100 => width(100);

  static Widget get empty => const SizedBox.shrink();
}
