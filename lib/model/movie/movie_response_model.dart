import 'package:json_annotation/json_annotation.dart';
import 'movie_model.dart';
import 'category_model.dart';

part 'movie_response_model.g.dart';

@JsonSerializable()
class MoviesResponse {
  @Json<PERSON>ey(name: 'success')
  final bool success;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'message')
  final String? message;

  @J<PERSON><PERSON><PERSON>(name: 'data')
  final List<MovieModel> data;

  @Json<PERSON>ey(name: 'total_count')
  final int? totalCount;

  @<PERSON>son<PERSON><PERSON>(name: 'page')
  final int? page;

  @Json<PERSON>ey(name: 'per_page')
  final int? perPage;

  @JsonKey(name: 'has_more')
  final bool? hasMore;

  MoviesResponse({
    required this.success,
    this.message,
    required this.data,
    this.totalCount,
    this.page,
    this.perPage,
    this.hasMore,
  });

  factory MoviesResponse.fromJson(Map<String, dynamic> json) =>
      _$MoviesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MoviesResponseToJson(this);
}

@JsonSerializable()
class CategoriesResponse {
  @Json<PERSON>ey(name: 'success')
  final bool success;

  @JsonKey(name: 'message')
  final String? message;

  @Json<PERSON>ey(name: 'data')
  final List<CategoryModel> data;

  CategoriesResponse({
    required this.success,
    this.message,
    required this.data,
  });

  factory CategoriesResponse.fromJson(Map<String, dynamic> json) =>
      _$CategoriesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CategoriesResponseToJson(this);
}

@JsonSerializable()
class FeaturedMoviesResponse {
  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'message')
  final String? message;

  @JsonKey(name: 'data')
  final List<MovieModel> data;

  FeaturedMoviesResponse({
    required this.success,
    this.message,
    required this.data,
  });

  factory FeaturedMoviesResponse.fromJson(Map<String, dynamic> json) =>
      _$FeaturedMoviesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FeaturedMoviesResponseToJson(this);
}

@JsonSerializable()
class MoviesByCategoryResponse {
  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'message')
  final String? message;

  @JsonKey(name: 'category')
  final CategoryModel? category;

  @JsonKey(name: 'data')
  final List<MovieModel> data;

  @JsonKey(name: 'total_count')
  final int? totalCount;

  @JsonKey(name: 'page')
  final int? page;

  @JsonKey(name: 'per_page')
  final int? perPage;

  @JsonKey(name: 'has_more')
  final bool? hasMore;

  MoviesByCategoryResponse({
    required this.success,
    this.message,
    this.category,
    required this.data,
    this.totalCount,
    this.page,
    this.perPage,
    this.hasMore,
  });

  factory MoviesByCategoryResponse.fromJson(Map<String, dynamic> json) =>
      _$MoviesByCategoryResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MoviesByCategoryResponseToJson(this);
}

@JsonSerializable()
class SearchMoviesResponse {
  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'message')
  final String? message;

  @JsonKey(name: 'query')
  final String? query;

  @JsonKey(name: 'data')
  final List<MovieModel> data;

  @JsonKey(name: 'total_count')
  final int? totalCount;

  @JsonKey(name: 'page')
  final int? page;

  @JsonKey(name: 'per_page')
  final int? perPage;

  @JsonKey(name: 'has_more')
  final bool? hasMore;

  SearchMoviesResponse({
    required this.success,
    this.message,
    this.query,
    required this.data,
    this.totalCount,
    this.page,
    this.perPage,
    this.hasMore,
  });

  factory SearchMoviesResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchMoviesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SearchMoviesResponseToJson(this);
}
