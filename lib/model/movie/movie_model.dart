import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'movie_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 0)
class MovieModel extends HiveObject {
  @HiveField(0)
  @<PERSON>son<PERSON>ey(name: 'id')
  final String id;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'title')
  final String title;

  @HiveField(2)
  @JsonKey(name: 'description')
  final String? description;

  @HiveField(3)
  @<PERSON>sonKey(name: 'youtube_url')
  final String youtubeUrl;

  @HiveField(4)
  @<PERSON>sonKey(name: 'thumbnail_url')
  final String thumbnailUrl;

  @HiveField(5)
  @<PERSON>sonKey(name: 'category')
  final String category;

  @HiveField(6)
  @JsonKey(name: 'duration')
  final String? duration;

  @HiveField(7)
  @<PERSON>sonKey(name: 'rating')
  final double? rating;

  @HiveField(8)
  @<PERSON>son<PERSON>ey(name: 'release_year')
  final int? releaseYear;

  @HiveField(9)
  @<PERSON>son<PERSON>ey(name: 'genre')
  final List<String>? genre;

  @HiveField(10)
  @<PERSON>sonKey(name: 'is_featured')
  final bool isFeatured;

  @HiveField(11)
  @JsonKey(name: 'view_count')
  final int? viewCount;

  @HiveField(12)
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  @HiveField(13)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  MovieModel({
    required this.id,
    required this.title,
    this.description,
    required this.youtubeUrl,
    required this.thumbnailUrl,
    required this.category,
    this.duration,
    this.rating,
    this.releaseYear,
    this.genre,
    this.isFeatured = false,
    this.viewCount,
    this.createdAt,
    this.updatedAt,
  });

  factory MovieModel.fromJson(Map<String, dynamic> json) =>
      _$MovieModelFromJson(json);

  Map<String, dynamic> toJson() => _$MovieModelToJson(this);

  // Helper method to extract YouTube video ID from URL
  String? get youtubeVideoId {
    final RegExp regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(youtubeUrl);
    return match?.group(1);
  }

  // Helper method to get formatted duration
  String get formattedDuration {
    if (duration == null) return 'Unknown';
    return duration!;
  }

  // Helper method to get formatted rating
  String get formattedRating {
    if (rating == null) return 'N/A';
    return rating!.toStringAsFixed(1);
  }

  // Helper method to get genre string
  String get genreString {
    if (genre == null || genre!.isEmpty) return 'Unknown';
    return genre!.join(', ');
  }

  @override
  String toString() {
    return 'MovieModel(id: $id, title: $title, category: $category, youtubeUrl: $youtubeUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MovieModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method for updating movie data
  MovieModel copyWith({
    String? id,
    String? title,
    String? description,
    String? youtubeUrl,
    String? thumbnailUrl,
    String? category,
    String? duration,
    double? rating,
    int? releaseYear,
    List<String>? genre,
    bool? isFeatured,
    int? viewCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MovieModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      youtubeUrl: youtubeUrl ?? this.youtubeUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      category: category ?? this.category,
      duration: duration ?? this.duration,
      rating: rating ?? this.rating,
      releaseYear: releaseYear ?? this.releaseYear,
      genre: genre ?? this.genre,
      isFeatured: isFeatured ?? this.isFeatured,
      viewCount: viewCount ?? this.viewCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
