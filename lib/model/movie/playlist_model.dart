import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'movie_model.dart';

part 'playlist_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 2)
class PlaylistModel extends HiveObject {
  @HiveField(0)
  @<PERSON>son<PERSON>ey(name: 'id')
  final String id;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'name')
  final String name;

  @HiveField(2)
  @<PERSON>son<PERSON><PERSON>(name: 'description')
  final String? description;

  @HiveField(3)
  @<PERSON>son<PERSON>ey(name: 'movie_ids')
  final List<String> movieIds;

  @HiveField(4)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_default')
  final bool isDefault;

  @HiveField(5)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;

  @HiveField(6)
  @<PERSON>son<PERSON>ey(name: 'updated_at')
  final DateTime updatedAt;

  PlaylistModel({
    required this.id,
    required this.name,
    this.description,
    required this.movieIds,
    this.isDefault = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PlaylistModel.fromJson(Map<String, dynamic> json) =>
      _$PlaylistModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlaylistModelToJson(this);

  // Helper method to get movie count
  int get movieCount => movieIds.length;

  // Helper method to check if playlist contains a movie
  bool containsMovie(String movieId) => movieIds.contains(movieId);

  // Helper method to add a movie to playlist
  PlaylistModel addMovie(String movieId) {
    if (containsMovie(movieId)) return this;
    
    final updatedMovieIds = List<String>.from(movieIds)..add(movieId);
    return copyWith(
      movieIds: updatedMovieIds,
      updatedAt: DateTime.now(),
    );
  }

  // Helper method to remove a movie from playlist
  PlaylistModel removeMovie(String movieId) {
    if (!containsMovie(movieId)) return this;
    
    final updatedMovieIds = List<String>.from(movieIds)..remove(movieId);
    return copyWith(
      movieIds: updatedMovieIds,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'PlaylistModel(id: $id, name: $name, movieCount: $movieCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlaylistModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Copy with method for updating playlist data
  PlaylistModel copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? movieIds,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PlaylistModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      movieIds: movieIds ?? this.movieIds,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Factory method to create default "My Playlist"
  factory PlaylistModel.createDefault() {
    final now = DateTime.now();
    return PlaylistModel(
      id: 'default_playlist',
      name: 'My Playlist',
      description: 'Your saved movies',
      movieIds: [],
      isDefault: true,
      createdAt: now,
      updatedAt: now,
    );
  }
}
