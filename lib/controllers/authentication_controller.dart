import 'package:my_video/app_imports.dart';

class AuthenticationController extends GetxController {
  final AuthenticationRepository _authRepository =
      getIt<AuthenticationRepository>();

  // Observables
  final Rx<ApiStatus> _apiStatus = ApiStatus.initial.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _userToken = ''.obs;
  final Rx<Map<String, dynamic>?> _userData = Rx<Map<String, dynamic>?>(null);

  // Getters
  ApiStatus get apiStatus => _apiStatus.value;
  bool get isLoggedIn => _isLoggedIn.value;
  String get userToken => _userToken.value;
  Map<String, dynamic>? get userData => _userData.value;

  // Form controllers
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  // Form keys
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();

  @override
  void onInit() {
    super.onInit();
    _checkLoginStatus();
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  void _checkLoginStatus() {
    final token = AppSharedPreference.getUserToken();
    final userData = AppSharedPreference.getUserData();

    if (token != null && token.isNotEmpty) {
      _userToken.value = token;
      _userData.value = userData;
      _isLoggedIn.value = true;
    }
  }

  Future<void> login() async {
    if (!loginFormKey.currentState!.validate()) {
      return;
    }

    try {
      _apiStatus.value = ApiStatus.loading;

      final hasInternet = await ConnectivityHelper.hasInternetConnection();
      if (!hasInternet) {
        _apiStatus.value = ApiStatus.noInternet;
        AppHelper.showToast('no_internet_connection'.tr, isError: true);
        return;
      }

      final result = await _authRepository.login(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );

      if (result['success'] == true) {
        final token = result['data']['token'] as String;
        final userData = result['data']['user'] as Map<String, dynamic>;

        // Save to local storage
        await AppSharedPreference.setUserToken(token);
        await AppSharedPreference.setUserData(userData);

        // Update observables
        _userToken.value = token;
        _userData.value = userData;
        _isLoggedIn.value = true;
        _apiStatus.value = ApiStatus.success;

        AppHelper.showToast('login_successful'.tr);

        // Navigate to home or dashboard
        // Get.offAllNamed(AppRoutes.home);
      } else {
        _apiStatus.value = ApiStatus.error;
        AppHelper.showToast(result['message'] ?? 'login_failed'.tr,
            isError: true);
      }
    } catch (e) {
      _apiStatus.value = ApiStatus.error;
      AppHelper.logError('Login error', e);
      AppHelper.showToast('something_went_wrong'.tr, isError: true);
    }
  }

  Future<void> logout() async {
    try {
      _apiStatus.value = ApiStatus.loading;

      // Call logout API if needed
      await _authRepository.logout();

      // Clear local storage
      await AppSharedPreference.logout();

      // Reset observables
      _userToken.value = '';
      _userData.value = null;
      _isLoggedIn.value = false;
      _apiStatus.value = ApiStatus.initial;

      // Clear form controllers
      emailController.clear();
      passwordController.clear();

      AppHelper.showToast('logout_successful'.tr);

      // Navigate to login
      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      _apiStatus.value = ApiStatus.error;
      AppHelper.logError('Logout error', e);
      AppHelper.showToast('something_went_wrong'.tr, isError: true);
    }
  }

  String? validateEmail(String? value) {
    return ValidationHelper.validateEmail(value);
  }

  String? validatePassword(String? value) {
    return ValidationHelper.validatePassword(value);
  }
}
