import 'package:my_video/app_imports.dart';

class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final String duration;
  final List<String> features;
  final bool isPopular;
  final bool isCurrentPlan;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.duration,
    required this.features,
    this.isPopular = false,
    this.isCurrentPlan = false,
  });
}

class SubscriptionController extends GetxController {
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Current subscription status
  bool _isPremiumUser = false;
  bool get isPremiumUser => _isPremiumUser;

  String? _currentPlanId;
  String? get currentPlanId => _currentPlanId;

  // Available subscription plans
  List<SubscriptionPlan> _subscriptionPlans = [];
  List<SubscriptionPlan> get subscriptionPlans => _subscriptionPlans;

  @override
  void onInit() {
    super.onInit();
    _loadSubscriptionData();
    _initializeSubscriptionPlans();
  }

  void _loadSubscriptionData() {
    try {
      // Load subscription status from local storage
      _isPremiumUser = HiveHelper.getSetting<bool>('is_premium_user', defaultValue: false) ?? false;
      _currentPlanId = HiveHelper.getSetting<String>('current_plan_id');
      
      _logger.i('Loaded subscription status: isPremium=$_isPremiumUser, planId=$_currentPlanId');
      update();
    } catch (e) {
      _logger.e('Error loading subscription data: $e');
    }
  }

  void _initializeSubscriptionPlans() {
    _subscriptionPlans = [
      SubscriptionPlan(
        id: 'free',
        name: 'Free',
        description: 'Basic access with ads',
        price: 0.0,
        duration: 'Forever',
        features: [
          'Watch unlimited movies',
          'Standard quality streaming',
          'Ads between videos',
          'Basic playlist features',
        ],
        isCurrentPlan: !_isPremiumUser,
      ),
      SubscriptionPlan(
        id: 'monthly',
        name: 'Premium Monthly',
        description: 'Full access without ads',
        price: 9.99,
        duration: 'per month',
        features: [
          'Ad-free experience',
          'HD quality streaming',
          'Unlimited playlists',
          'Download for offline viewing',
          'Priority customer support',
          'Early access to new features',
        ],
        isPopular: true,
        isCurrentPlan: _isPremiumUser && _currentPlanId == 'monthly',
      ),
      SubscriptionPlan(
        id: 'yearly',
        name: 'Premium Yearly',
        description: 'Best value - Save 40%',
        price: 59.99,
        duration: 'per year',
        features: [
          'All Premium Monthly features',
          'Save \$60 per year',
          'Exclusive yearly subscriber perks',
          'Priority feature requests',
          'Advanced analytics',
        ],
        isCurrentPlan: _isPremiumUser && _currentPlanId == 'yearly',
      ),
    ];
    update();
  }

  Future<void> subscribeToPlan(SubscriptionPlan plan) async {
    if (plan.id == 'free') {
      // Handle free plan (cancel subscription)
      await _cancelSubscription();
      return;
    }

    _setLoading(true);
    try {
      // TODO: Implement Google Play Store IAP integration
      // For now, simulate subscription process
      await Future.delayed(const Duration(seconds: 2));
      
      // Update subscription status
      await HiveHelper.saveSetting('is_premium_user', true);
      await HiveHelper.saveSetting('current_plan_id', plan.id);
      await HiveHelper.saveSetting('subscription_start_date', DateTime.now().toIso8601String());
      
      _isPremiumUser = true;
      _currentPlanId = plan.id;
      
      // Update plan status
      _initializeSubscriptionPlans();
      
      _showSuccess('Successfully subscribed to ${plan.name}!');
      _logger.i('Subscribed to plan: ${plan.name}');
    } catch (e) {
      _logger.e('Error subscribing to plan: $e');
      _showError('Failed to subscribe. Please try again.');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _cancelSubscription() async {
    _setLoading(true);
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const AppText(
            text: 'Cancel Subscription',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          content: const AppText(
            text: 'Are you sure you want to cancel your premium subscription? You will lose access to premium features.',
            fontSize: 14,
            color: AppColorConstants.textSecondary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const AppText(
                text: 'Keep Subscription',
                color: AppColorConstants.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const AppText(
                text: 'Cancel',
                color: AppColorConstants.colorRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Update subscription status
        await HiveHelper.saveSetting('is_premium_user', false);
        await HiveHelper.deleteSetting('current_plan_id');
        await HiveHelper.deleteSetting('subscription_start_date');
        
        _isPremiumUser = false;
        _currentPlanId = null;
        
        // Update plan status
        _initializeSubscriptionPlans();
        
        _showSuccess('Subscription cancelled successfully');
        _logger.i('Subscription cancelled');
      }
    } catch (e) {
      _logger.e('Error cancelling subscription: $e');
      _showError('Failed to cancel subscription');
    } finally {
      _setLoading(false);
    }
  }

  void restorePurchases() {
    // TODO: Implement restore purchases functionality
    _showInfo('Restore purchases functionality will be implemented with Google Play Store integration');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    update();
  }

  void _showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorGreen,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  void _showInfo(String message) {
    Get.snackbar(
      'Info',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  @override
  void onClose() {
    super.onClose();
  }
}
