import 'package:my_video/app_imports.dart';

class SettingsController extends GetxController {
  final Logger _logger = Logger();

  // User profile data
  String _userName = 'Movie Lover';
  String get userName => _userName;

  String _userEmail = '<EMAIL>';
  String get userEmail => _userEmail;

  // App settings
  bool _notificationsEnabled = true;
  bool get notificationsEnabled => _notificationsEnabled;

  bool _autoPlayEnabled = true;
  bool get autoPlayEnabled => _autoPlayEnabled;

  bool _downloadOnWifiOnly = true;
  bool get downloadOnWifiOnly => _downloadOnWifiOnly;

  String _videoQuality = 'Auto';
  String get videoQuality => _videoQuality;

  List<String> _videoQualityOptions = ['Auto', 'HD', 'SD', 'Low'];
  List<String> get videoQualityOptions => _videoQualityOptions;

  // App info
  String _appVersion = '1.0.0';
  String get appVersion => _appVersion;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  void _loadSettings() {
    try {
      // Load user profile
      _userName = HiveHelper.getSetting<String>('user_name', defaultValue: 'Movie Lover') ?? 'Movie Lover';
      _userEmail = HiveHelper.getSetting<String>('user_email', defaultValue: '<EMAIL>') ?? '<EMAIL>';
      
      // Load app settings
      _notificationsEnabled = HiveHelper.getSetting<bool>('notifications_enabled', defaultValue: true) ?? true;
      _autoPlayEnabled = HiveHelper.getSetting<bool>('auto_play_enabled', defaultValue: true) ?? true;
      _downloadOnWifiOnly = HiveHelper.getSetting<bool>('download_wifi_only', defaultValue: true) ?? true;
      _videoQuality = HiveHelper.getSetting<String>('video_quality', defaultValue: 'Auto') ?? 'Auto';
      
      _logger.i('Settings loaded successfully');
      update();
    } catch (e) {
      _logger.e('Error loading settings: $e');
    }
  }

  Future<void> updateNotifications(bool enabled) async {
    try {
      _notificationsEnabled = enabled;
      await HiveHelper.saveSetting('notifications_enabled', enabled);
      update();
      _logger.i('Notifications setting updated: $enabled');
    } catch (e) {
      _logger.e('Error updating notifications setting: $e');
    }
  }

  Future<void> updateAutoPlay(bool enabled) async {
    try {
      _autoPlayEnabled = enabled;
      await HiveHelper.saveSetting('auto_play_enabled', enabled);
      update();
      _logger.i('Auto-play setting updated: $enabled');
    } catch (e) {
      _logger.e('Error updating auto-play setting: $e');
    }
  }

  Future<void> updateDownloadWifiOnly(bool enabled) async {
    try {
      _downloadOnWifiOnly = enabled;
      await HiveHelper.saveSetting('download_wifi_only', enabled);
      update();
      _logger.i('Download WiFi-only setting updated: $enabled');
    } catch (e) {
      _logger.e('Error updating download WiFi-only setting: $e');
    }
  }

  Future<void> updateVideoQuality(String quality) async {
    try {
      _videoQuality = quality;
      await HiveHelper.saveSetting('video_quality', quality);
      update();
      _logger.i('Video quality setting updated: $quality');
    } catch (e) {
      _logger.e('Error updating video quality setting: $e');
    }
  }

  void openTermsOfService() {
    // TODO: Implement terms of service page
    Get.snackbar(
      'Terms of Service',
      'Terms of Service page will be implemented',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
    );
  }

  void openPrivacyPolicy() {
    // TODO: Implement privacy policy page
    Get.snackbar(
      'Privacy Policy',
      'Privacy Policy page will be implemented',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
    );
  }

  void contactSupport() {
    // TODO: Implement contact support
    Get.snackbar(
      'Contact Support',
      'Support contact functionality will be implemented',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
    );
  }

  void rateApp() {
    // TODO: Implement app rating
    Get.snackbar(
      'Rate App',
      'App rating functionality will be implemented',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
    );
  }

  void shareApp() {
    try {
      const shareText = '''
Check out MyVideo - the best movie streaming app!

Watch unlimited movies with high-quality streaming.
Download now and enjoy premium content!

#MyVideo #MovieStreaming #Entertainment
''';
      
      Share.share(shareText, subject: 'MyVideo - Movie Streaming App');
      _logger.i('App shared successfully');
    } catch (e) {
      _logger.e('Error sharing app: $e');
      Get.snackbar(
        'Error',
        'Failed to share app',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColorConstants.colorRed,
        colorText: AppColorConstants.textPrimary,
      );
    }
  }

  Future<void> clearCache() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const AppText(
            text: 'Clear Cache',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          content: const AppText(
            text: 'This will clear all cached data including downloaded thumbnails. Are you sure?',
            fontSize: 14,
            color: AppColorConstants.textSecondary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const AppText(
                text: 'Cancel',
                color: AppColorConstants.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const AppText(
                text: 'Clear',
                color: AppColorConstants.colorRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // TODO: Implement actual cache clearing
        await Future.delayed(const Duration(seconds: 1)); // Simulate clearing
        
        Get.snackbar(
          'Success',
          'Cache cleared successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColorConstants.colorGreen,
          colorText: AppColorConstants.textPrimary,
        );
        
        _logger.i('Cache cleared');
      }
    } catch (e) {
      _logger.e('Error clearing cache: $e');
      Get.snackbar(
        'Error',
        'Failed to clear cache',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColorConstants.colorRed,
        colorText: AppColorConstants.textPrimary,
      );
    }
  }

  Future<void> logout() async {
    try {
      // Show confirmation dialog
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const AppText(
            text: 'Logout',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColorConstants.textPrimary,
          ),
          content: const AppText(
            text: 'Are you sure you want to logout? You will need to login again to access your account.',
            fontSize: 14,
            color: AppColorConstants.textSecondary,
          ),
          backgroundColor: AppColorConstants.surfaceColor,
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const AppText(
                text: 'Cancel',
                color: AppColorConstants.textSecondary,
              ),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const AppText(
                text: 'Logout',
                color: AppColorConstants.colorRed,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Clear user session data
        await HiveHelper.deleteSetting('user_name');
        await HiveHelper.deleteSetting('user_email');
        await HiveHelper.deleteSetting('is_premium_user');
        await HiveHelper.deleteSetting('current_plan_id');
        
        // Navigate to login page
        Get.offAllNamed(AppRoutes.login);
        
        _logger.i('User logged out');
      }
    } catch (e) {
      _logger.e('Error during logout: $e');
      Get.snackbar(
        'Error',
        'Failed to logout',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColorConstants.colorRed,
        colorText: AppColorConstants.textPrimary,
      );
    }
  }

  Map<String, int> getDatabaseInfo() {
    return HiveHelper.getDatabaseInfo();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
