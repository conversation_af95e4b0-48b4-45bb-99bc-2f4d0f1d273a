import 'package:my_video/app_imports.dart';

class HomeController extends GetxController {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Data
  List<MovieModel> _featuredMovies = [];
  List<CategoryModel> _categories = [];
  Map<String, List<MovieModel>> _moviesByCategory = {};

  List<MovieModel> get featuredMovies => _featuredMovies;
  List<CategoryModel> get categories => _categories;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    _setLoading(true);
    try {
      await Future.wait([
        loadFeaturedMovies(),
        loadCategories(),
      ]);
      await loadMoviesForCategories();
    } catch (e) {
      _logger.e('Error loading initial data: $e');
      _showError('Failed to load data. Please try again.');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadFeaturedMovies() async {
    try {
      final response = await _movieRepository.getFeaturedMovies();
      if (response.success) {
        _featuredMovies = response.data;
        _logger.i('Loaded ${_featuredMovies.length} featured movies');
      }
    } catch (e) {
      _logger.e('Error loading featured movies: $e');
      // Try to load from cache
      _featuredMovies = HiveHelper.getFeaturedMovies();
    }
  }

  Future<void> loadCategories() async {
    try {
      final response = await _movieRepository.getAllCategories();
      if (response.success) {
        _categories = response.data;
        _logger.i('Loaded ${_categories.length} categories');
      }
    } catch (e) {
      _logger.e('Error loading categories: $e');
      // Try to load from cache
      _categories = HiveHelper.getAllCategories();
    }
  }

  Future<void> loadMoviesForCategories() async {
    for (final category in _categories) {
      try {
        final response = await _movieRepository.getMoviesByCategory(
          category.name,
          perPage: 10,
        );
        if (response.success) {
          _moviesByCategory[category.name] = response.data;
          _logger.i('Loaded ${response.data.length} movies for ${category.name}');
        }
      } catch (e) {
        _logger.e('Error loading movies for ${category.name}: $e');
        // Try to load from cache
        _moviesByCategory[category.name] = HiveHelper.getMoviesByCategory(category.name);
      }
    }
  }

  List<MovieModel> getMoviesByCategory(String categoryName) {
    return _moviesByCategory[categoryName] ?? [];
  }

  Future<void> refreshData() async {
    await loadInitialData();
  }

  void playMovie(MovieModel movie) {
    // TODO: Navigate to video player
    _logger.i('Playing movie: ${movie.title}');
    Get.snackbar(
      'Playing Movie',
      movie.title,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 2),
    );
  }

  void viewAllMovies(CategoryModel category) {
    // TODO: Navigate to category movies page
    _logger.i('View all movies for category: ${category.name}');
    Get.snackbar(
      'Category',
      'View all ${category.name} movies',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.primaryColor,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 2),
    );
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    update();
  }

  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  @override
  void onClose() {
    super.onClose();
  }
}
