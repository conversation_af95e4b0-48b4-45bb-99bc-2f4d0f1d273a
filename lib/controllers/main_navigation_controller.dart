import 'package:my_video/app_imports.dart';

class MainNavigationController extends GetxController {
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  final List<Widget> _pages = [
    const HomePage(),
    const MyPlaylistPage(),
    const SubscriptionPage(),
    const SettingsPage(),
  ];

  List<Widget> get pages => _pages;

  void changeTab(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      update();
    }
  }

  void showAddMovieDialog() {
    Get.dialog(
      const AddMovieDialog(),
      barrierDismissible: true,
    );
  }

  @override
  void onInit() {
    super.onInit();
    // Initialize any required data
  }

  @override
  void onClose() {
    super.onClose();
  }
}

// Home Page Implementation
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'MyVideo',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  Get.to(
                    () => const SearchPage(),
                    transition: Transition.rightToLeft,
                  );
                },
              ),
            ],
          ),
          body: controller.isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: controller.refreshData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Featured Movies Banner
                        if (controller.featuredMovies.isNotEmpty) ...[
                          _buildFeaturedBanner(controller),
                          Space.height(24),
                        ],

                        // Category Carousels
                        ...controller.categories.map((category) {
                          final movies =
                              controller.getMoviesByCategory(category.name);
                          if (movies.isEmpty) return const SizedBox.shrink();

                          return Column(
                            children: [
                              _buildCategorySection(
                                  category, movies, controller),
                              Space.height(24),
                            ],
                          );
                        }),

                        // Bottom padding
                        Space.height(100),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildFeaturedBanner(HomeController controller) {
    if (controller.featuredMovies.isEmpty) {
      return SizedBox(
        height: MySize.height(220),
        child: LoadingManager.buildLoadingWidget(
            message: 'Loading featured movies...'),
      );
    }

    return SizedBox(
      height: MySize.height(220),
      child: PageView.builder(
        itemCount: controller.featuredMovies.length,
        controller: PageController(viewportFraction: 0.9),
        itemBuilder: (context, index) {
          final movie = controller.featuredMovies[index];
          return Container(
            margin: EdgeInsets.symmetric(horizontal: MySize.width(8)),
            child: BannerMovieCard(
              movie: movie,
              onTap: () => controller.playMovie(movie),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategorySection(CategoryModel category, List<MovieModel> movies,
      HomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                text: category.name,
                fontSize: MySize.fontSize(18),
                fontWeight: FontWeight.bold,
                color: AppColorConstants.textPrimary,
              ),
              GestureDetector(
                onTap: () => controller.viewAllMovies(category),
                child: AppText(
                  text: 'See All',
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: movies.length + 2, // +2 for ad placeholders
            itemBuilder: (context, index) {
              // First item: Banner ad
              if (index == 0) {
                final bannerAd = AdsManager.getBannerAdWidget();
                return bannerAd ?? AdsManager.buildAdPlaceholder();
              }

              // Last item: Banner ad
              if (index == movies.length + 1) {
                final bannerAd = AdsManager.getBannerAdWidget();
                return bannerAd ?? AdsManager.buildAdPlaceholder();
              }

              // Movie items
              final movieIndex = index - 1;
              final movie = movies[movieIndex];
              return MovieCard(
                movie: movie,
                onTap: () => controller.playMovie(movie),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
      ],
    );
  }
}

class MyPlaylistPage extends StatelessWidget {
  const MyPlaylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PlaylistController>(
      init: PlaylistController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'My Playlist',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            actions: [
              if (controller.playlistMovies.isNotEmpty) ...[
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: controller.sharePlaylist,
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'clear') {
                      controller.clearPlaylist();
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'clear',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all,
                              color: AppColorConstants.colorRed),
                          SizedBox(width: 8),
                          Text('Clear Playlist'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          body: controller.isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: controller.refreshPlaylist,
                  child: controller.playlistMovies.isEmpty
                      ? _buildEmptyState()
                      : _buildPlaylistGrid(controller),
                ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.playlist_play_outlined,
            size: MySize.height(80),
            color: AppColorConstants.textHint,
          ),
          Space.height(16),
          AppText(
            text: 'Your playlist is empty',
            fontSize: MySize.fontSize(18),
            fontWeight: FontWeight.w600,
            color: AppColorConstants.textPrimary,
          ),
          Space.height(8),
          AppText(
            text:
                'Add movies to your playlist by tapping the + button\nwhile watching any movie',
            fontSize: MySize.fontSize(14),
            color: AppColorConstants.textSecondary,
            textAlign: TextAlign.center,
          ),
          Space.height(24),
          AppButton(
            text: 'Browse Movies',
            onPressed: () {
              // Switch to home tab
              final mainController = Get.find<MainNavigationController>();
              mainController.changeTab(0);
            },
            backgroundColor: AppColorConstants.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistGrid(PlaylistController controller) {
    return Padding(
      padding: EdgeInsets.all(MySize.width(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Playlist info
          Row(
            children: [
              Icon(
                Icons.playlist_play,
                color: AppColorConstants.primaryColor,
                size: MySize.height(20),
              ),
              Space.width(8),
              AppText(
                text: '${controller.playlistMovies.length} movies',
                fontSize: MySize.fontSize(16),
                color: AppColorConstants.textSecondary,
              ),
            ],
          ),
          Space.height(16),

          // Movies grid
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.7,
                crossAxisSpacing: MySize.width(12),
                mainAxisSpacing: MySize.height(16),
              ),
              itemCount: controller.playlistMovies.length,
              itemBuilder: (context, index) {
                final movie = controller.playlistMovies[index];
                return _buildPlaylistMovieCard(movie, controller);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistMovieCard(
      MovieModel movie, PlaylistController controller) {
    return GestureDetector(
      onTap: () => controller.playMovie(movie),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(MySize.radius(12)),
          color: AppColorConstants.cardColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail with remove button
            Expanded(
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(MySize.radius(12)),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: movie.thumbnailUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: AppColorConstants.cardColor,
                        child: Center(
                          child: Icon(
                            Icons.movie_outlined,
                            size: MySize.height(40),
                            color: AppColorConstants.textHint,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: AppColorConstants.cardColor,
                        child: Center(
                          child: Icon(
                            Icons.broken_image_outlined,
                            size: MySize.height(40),
                            color: AppColorConstants.textHint,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Play button overlay
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.center,
                          end: Alignment.center,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.1),
                          ],
                        ),
                      ),
                      child: Center(
                        child: Container(
                          padding: EdgeInsets.all(MySize.height(8)),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.play_arrow,
                            color: AppColorConstants.textPrimary,
                            size: MySize.height(24),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Remove button
                  Positioned(
                    top: MySize.height(8),
                    right: MySize.width(8),
                    child: GestureDetector(
                      onTap: () => controller.removeFromPlaylist(movie),
                      child: Container(
                        padding: EdgeInsets.all(MySize.height(4)),
                        decoration: BoxDecoration(
                          color: AppColorConstants.colorRed,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          color: AppColorConstants.textPrimary,
                          size: MySize.height(16),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Movie info
            Padding(
              padding: EdgeInsets.all(MySize.width(8)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    text: movie.title,
                    fontSize: MySize.fontSize(14),
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.textPrimary,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (movie.rating != null) ...[
                    Space.height(4),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: MySize.height(12),
                          color: Colors.amber,
                        ),
                        Space.width(4),
                        AppText(
                          text: movie.formattedRating,
                          fontSize: MySize.fontSize(12),
                          color: AppColorConstants.textSecondary,
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SubscriptionPage extends StatelessWidget {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SubscriptionController>(
      init: SubscriptionController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'Subscription',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            actions: [
              TextButton(
                onPressed: controller.restorePurchases,
                child: const AppText(
                  text: 'Restore',
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          body: controller.isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: EdgeInsets.all(MySize.width(16)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Current Status
                      _buildCurrentStatus(controller),
                      Space.height(24),

                      // Premium Benefits
                      _buildPremiumBenefits(),
                      Space.height(24),

                      // Subscription Plans
                      const AppText(
                        text: 'Choose Your Plan',
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColorConstants.textPrimary,
                      ),
                      Space.height(16),

                      ...controller.subscriptionPlans.map((plan) {
                        return Column(
                          children: [
                            _buildPlanCard(plan, controller),
                            Space.height(16),
                          ],
                        );
                      }),

                      Space.height(24),

                      // Terms and Privacy
                      _buildTermsAndPrivacy(),

                      Space.height(100), // Bottom padding
                    ],
                  ),
                ),
        );
      },
    );
  }

  Widget _buildCurrentStatus(SubscriptionController controller) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: BoxDecoration(
        color: controller.isPremiumUser
            ? AppColorConstants.primaryColor.withValues(alpha: 0.1)
            : AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: controller.isPremiumUser
              ? AppColorConstants.primaryColor
              : AppColorConstants.dividerColor,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                controller.isPremiumUser ? Icons.star : Icons.person_outline,
                color: controller.isPremiumUser
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.textSecondary,
                size: MySize.height(24),
              ),
              Space.width(8),
              AppText(
                text: controller.isPremiumUser ? 'Premium Member' : 'Free User',
                fontSize: MySize.fontSize(18),
                fontWeight: FontWeight.bold,
                color: controller.isPremiumUser
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.textPrimary,
              ),
            ],
          ),
          Space.height(8),
          AppText(
            text: controller.isPremiumUser
                ? 'You have access to all premium features'
                : 'Upgrade to premium for an ad-free experience',
            fontSize: MySize.fontSize(14),
            color: AppColorConstants.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumBenefits() {
    final benefits = [
      {
        'icon': Icons.block,
        'title': 'Ad-Free Experience',
        'description': 'Watch without interruptions'
      },
      {
        'icon': Icons.hd,
        'title': 'HD Quality',
        'description': 'Crystal clear video streaming'
      },
      {
        'icon': Icons.download,
        'title': 'Offline Downloads',
        'description': 'Watch anywhere, anytime'
      },
      {
        'icon': Icons.support_agent,
        'title': 'Priority Support',
        'description': '24/7 customer assistance'
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          text: 'Premium Benefits',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(16),
        ...benefits.map((benefit) {
          return Padding(
            padding: EdgeInsets.only(bottom: MySize.height(12)),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(MySize.height(8)),
                  decoration: BoxDecoration(
                    color:
                        AppColorConstants.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.radius(8)),
                  ),
                  child: Icon(
                    benefit['icon'] as IconData,
                    color: AppColorConstants.primaryColor,
                    size: MySize.height(20),
                  ),
                ),
                Space.width(12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        text: benefit['title'] as String,
                        fontSize: MySize.fontSize(16),
                        fontWeight: FontWeight.w600,
                        color: AppColorConstants.textPrimary,
                      ),
                      AppText(
                        text: benefit['description'] as String,
                        fontSize: MySize.fontSize(14),
                        color: AppColorConstants.textSecondary,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildPlanCard(
      SubscriptionPlan plan, SubscriptionController controller) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: plan.isCurrentPlan
            ? AppColorConstants.primaryColor.withValues(alpha: 0.1)
            : AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: plan.isPopular
              ? AppColorConstants.primaryColor
              : plan.isCurrentPlan
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.dividerColor,
          width: plan.isPopular || plan.isCurrentPlan ? 2 : 1,
        ),
      ),
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.all(MySize.width(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Plan header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppText(
                          text: plan.name,
                          fontSize: MySize.fontSize(18),
                          fontWeight: FontWeight.bold,
                          color: AppColorConstants.textPrimary,
                        ),
                        AppText(
                          text: plan.description,
                          fontSize: MySize.fontSize(14),
                          color: AppColorConstants.textSecondary,
                        ),
                      ],
                    ),
                    if (plan.price > 0) ...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          AppText(
                            text: '\$${plan.price.toStringAsFixed(2)}',
                            fontSize: MySize.fontSize(20),
                            fontWeight: FontWeight.bold,
                            color: AppColorConstants.primaryColor,
                          ),
                          AppText(
                            text: plan.duration,
                            fontSize: MySize.fontSize(12),
                            color: AppColorConstants.textSecondary,
                          ),
                        ],
                      ),
                    ] else ...[
                      AppText(
                        text: 'FREE',
                        fontSize: MySize.fontSize(18),
                        fontWeight: FontWeight.bold,
                        color: AppColorConstants.colorGreen,
                      ),
                    ],
                  ],
                ),

                Space.height(16),

                // Features
                ...plan.features.map((feature) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: MySize.height(8)),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: AppColorConstants.colorGreen,
                          size: MySize.height(16),
                        ),
                        Space.width(8),
                        Expanded(
                          child: AppText(
                            text: feature,
                            fontSize: MySize.fontSize(14),
                            color: AppColorConstants.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  );
                }),

                Space.height(16),

                // Action button
                SizedBox(
                  width: double.infinity,
                  child: AppButton(
                    text: plan.isCurrentPlan
                        ? 'Current Plan'
                        : plan.id == 'free'
                            ? 'Downgrade to Free'
                            : 'Subscribe Now',
                    onPressed: plan.isCurrentPlan
                        ? null
                        : () => controller.subscribeToPlan(plan),
                    backgroundColor: plan.isCurrentPlan
                        ? AppColorConstants.cardColor
                        : plan.id == 'free'
                            ? AppColorConstants.colorRed
                            : AppColorConstants.primaryColor,
                    textColor: plan.isCurrentPlan
                        ? AppColorConstants.textSecondary
                        : AppColorConstants.textPrimary,
                  ),
                ),
              ],
            ),
          ),

          // Popular badge
          if (plan.isPopular)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.width(12),
                  vertical: MySize.height(4),
                ),
                decoration: BoxDecoration(
                  color: AppColorConstants.primaryColor,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(MySize.radius(12)),
                    bottomLeft: Radius.circular(MySize.radius(12)),
                  ),
                ),
                child: AppText(
                  text: 'POPULAR',
                  fontSize: MySize.fontSize(10),
                  fontWeight: FontWeight.bold,
                  color: AppColorConstants.textPrimary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTermsAndPrivacy() {
    return Column(
      children: [
        const Divider(color: AppColorConstants.dividerColor),
        Space.height(16),
        AppText(
          text:
              'By subscribing, you agree to our Terms of Service and Privacy Policy. Subscriptions automatically renew unless cancelled.',
          fontSize: MySize.fontSize(12),
          color: AppColorConstants.textHint,
          textAlign: TextAlign.center,
        ),
        Space.height(8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                // TODO: Open Terms of Service
              },
              child: AppText(
                text: 'Terms of Service',
                fontSize: MySize.fontSize(12),
                color: AppColorConstants.primaryColor,
                textDecoration: TextDecoration.underline,
              ),
            ),
            AppText(
              text: ' • ',
              fontSize: MySize.fontSize(12),
              color: AppColorConstants.textHint,
            ),
            GestureDetector(
              onTap: () {
                // TODO: Open Privacy Policy
              },
              child: AppText(
                text: 'Privacy Policy',
                fontSize: MySize.fontSize(12),
                color: AppColorConstants.primaryColor,
                textDecoration: TextDecoration.underline,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingsController>(
      init: SettingsController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'Settings',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.all(MySize.width(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User Profile Section
                _buildUserProfile(controller),
                Space.height(24),

                // App Settings Section
                _buildAppSettings(controller),
                Space.height(24),

                // Support Section
                _buildSupportSection(controller),
                Space.height(24),

                // About Section
                _buildAboutSection(controller),
                Space.height(24),

                // Danger Zone
                _buildDangerZone(controller),

                Space.height(100), // Bottom padding
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserProfile(SettingsController controller) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(MySize.width(16)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: MySize.height(30),
                backgroundColor: AppColorConstants.primaryColor,
                child: AppText(
                  text: controller.userName.isNotEmpty
                      ? controller.userName[0].toUpperCase()
                      : 'U',
                  fontSize: MySize.fontSize(24),
                  fontWeight: FontWeight.bold,
                  color: AppColorConstants.textPrimary,
                ),
              ),
              Space.width(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      text: controller.userName,
                      fontSize: MySize.fontSize(18),
                      fontWeight: FontWeight.bold,
                      color: AppColorConstants.textPrimary,
                    ),
                    AppText(
                      text: controller.userEmail,
                      fontSize: MySize.fontSize(14),
                      color: AppColorConstants.textSecondary,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.edit,
                color: AppColorConstants.textSecondary,
                size: MySize.height(20),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettings(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: 'App Settings',
          fontSize: MySize.fontSize(18),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(16),

        // Notifications
        _buildSettingTile(
          icon: Icons.notifications_outlined,
          title: 'Notifications',
          subtitle: 'Receive updates and alerts',
          trailing: Switch(
            value: controller.notificationsEnabled,
            onChanged: controller.updateNotifications,
            activeColor: AppColorConstants.primaryColor,
          ),
        ),

        // Auto-play
        _buildSettingTile(
          icon: Icons.play_circle_outline,
          title: 'Auto-play',
          subtitle: 'Automatically play next video',
          trailing: Switch(
            value: controller.autoPlayEnabled,
            onChanged: controller.updateAutoPlay,
            activeColor: AppColorConstants.primaryColor,
          ),
        ),

        // Download on WiFi only
        _buildSettingTile(
          icon: Icons.wifi,
          title: 'Download on WiFi only',
          subtitle: 'Save mobile data',
          trailing: Switch(
            value: controller.downloadOnWifiOnly,
            onChanged: controller.updateDownloadWifiOnly,
            activeColor: AppColorConstants.primaryColor,
          ),
        ),

        // Video Quality
        _buildSettingTile(
          icon: Icons.high_quality,
          title: 'Video Quality',
          subtitle: 'Current: ${controller.videoQuality}',
          trailing: Icon(
            Icons.arrow_forward_ios,
            color: AppColorConstants.textSecondary,
            size: MySize.height(16),
          ),
          onTap: () => _showVideoQualityDialog(controller),
        ),
      ],
    );
  }

  Widget _buildSupportSection(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: 'Support',
          fontSize: MySize.fontSize(18),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(16),
        _buildSettingTile(
          icon: Icons.help_outline,
          title: 'Contact Support',
          subtitle: 'Get help with the app',
          onTap: controller.contactSupport,
        ),
        _buildSettingTile(
          icon: Icons.star_outline,
          title: 'Rate App',
          subtitle: 'Rate us on the App Store',
          onTap: controller.rateApp,
        ),
        _buildSettingTile(
          icon: Icons.share_outlined,
          title: 'Share App',
          subtitle: 'Tell your friends about MyVideo',
          onTap: controller.shareApp,
        ),
      ],
    );
  }

  Widget _buildAboutSection(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: 'About',
          fontSize: MySize.fontSize(18),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        Space.height(16),
        _buildSettingTile(
          icon: Icons.description_outlined,
          title: 'Terms of Service',
          subtitle: 'Read our terms and conditions',
          onTap: controller.openTermsOfService,
        ),
        _buildSettingTile(
          icon: Icons.privacy_tip_outlined,
          title: 'Privacy Policy',
          subtitle: 'How we protect your data',
          onTap: controller.openPrivacyPolicy,
        ),
        _buildSettingTile(
          icon: Icons.info_outline,
          title: 'App Version',
          subtitle: 'Version ${controller.appVersion}',
        ),
        _buildSettingTile(
          icon: Icons.storage_outlined,
          title: 'Storage Info',
          subtitle:
              'Database: ${controller.getDatabaseInfo()['movies']} movies, ${controller.getDatabaseInfo()['playlists']} playlists',
        ),
      ],
    );
  }

  Widget _buildDangerZone(SettingsController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          text: 'Danger Zone',
          fontSize: MySize.fontSize(18),
          fontWeight: FontWeight.bold,
          color: AppColorConstants.colorRed,
        ),
        Space.height(16),
        _buildSettingTile(
          icon: Icons.clear_all,
          title: 'Clear Cache',
          subtitle: 'Free up storage space',
          titleColor: AppColorConstants.colorRed,
          onTap: controller.clearCache,
        ),
        _buildSettingTile(
          icon: Icons.logout,
          title: 'Logout',
          subtitle: 'Sign out of your account',
          titleColor: AppColorConstants.colorRed,
          onTap: controller.logout,
        ),
      ],
    );
  }

  Widget _buildSettingTile({
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    Color? titleColor,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: MySize.height(8)),
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(MySize.height(8)),
          decoration: BoxDecoration(
            color: AppColorConstants.surfaceColor,
            borderRadius: BorderRadius.circular(MySize.radius(8)),
          ),
          child: Icon(
            icon,
            color: titleColor ?? AppColorConstants.textPrimary,
            size: MySize.height(20),
          ),
        ),
        title: AppText(
          text: title,
          fontSize: MySize.fontSize(16),
          fontWeight: FontWeight.w600,
          color: titleColor ?? AppColorConstants.textPrimary,
        ),
        subtitle: subtitle != null
            ? AppText(
                text: subtitle,
                fontSize: MySize.fontSize(14),
                color: AppColorConstants.textSecondary,
              )
            : null,
        trailing: trailing ??
            (onTap != null
                ? Icon(
                    Icons.arrow_forward_ios,
                    color: AppColorConstants.textSecondary,
                    size: MySize.height(16),
                  )
                : null),
        onTap: onTap,
        tileColor: AppColorConstants.cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(MySize.radius(8)),
        ),
      ),
    );
  }

  void _showVideoQualityDialog(SettingsController controller) {
    Get.dialog(
      AlertDialog(
        title: const AppText(
          text: 'Video Quality',
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.surfaceColor,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: controller.videoQualityOptions.map((quality) {
            return RadioListTile<String>(
              title: AppText(
                text: quality,
                fontSize: 16,
                color: AppColorConstants.textPrimary,
              ),
              value: quality,
              groupValue: controller.videoQuality,
              onChanged: (value) {
                if (value != null) {
                  controller.updateVideoQuality(value);
                  Get.back();
                }
              },
              activeColor: AppColorConstants.primaryColor,
            );
          }).toList(),
        ),
      ),
    );
  }
}

class AddMovieDialog extends StatelessWidget {
  const AddMovieDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddMovieController>(
      init: AddMovieController(),
      builder: (controller) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.radius(16)),
          ),
          child: Container(
            padding: EdgeInsets.all(MySize.height(24)),
            constraints: BoxConstraints(
              maxHeight: MySize.height(600),
              maxWidth: MySize.width(400),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        Icons.add_circle_outline,
                        color: AppColorConstants.primaryColor,
                        size: MySize.height(24),
                      ),
                      Space.width(8),
                      const AppText(
                        text: 'Add Movie to Playlist',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColorConstants.textPrimary,
                      ),
                    ],
                  ),
                  Space.height(20),

                  // YouTube URL Field
                  const AppText(
                    text: 'YouTube URL *',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.textPrimary,
                  ),
                  Space.height(8),
                  AppTextFormField(
                    controller: controller.urlController,
                    hintText: 'https://www.youtube.com/watch?v=...',
                    keyboardType: TextInputType.url,
                    suffixIcon: controller.isValidUrl
                        ? Icon(
                            Icons.check_circle,
                            color: AppColorConstants.colorGreen,
                            size: MySize.height(20),
                          )
                        : null,
                  ),
                  Space.height(16),

                  // Movie Title Field
                  const AppText(
                    text: 'Movie Title *',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.textPrimary,
                  ),
                  Space.height(8),
                  AppTextFormField(
                    controller: controller.titleController,
                    hintText: 'Enter movie title',
                  ),
                  Space.height(16),

                  // Category Dropdown
                  const AppText(
                    text: 'Category',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.textPrimary,
                  ),
                  Space.height(8),
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(horizontal: MySize.width(12)),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColorConstants.dividerColor),
                      borderRadius: BorderRadius.circular(MySize.radius(8)),
                      color: AppColorConstants.surfaceColor,
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: controller.selectedCategory,
                        isExpanded: true,
                        dropdownColor: AppColorConstants.surfaceColor,
                        style: TextStyle(
                          color: AppColorConstants.textPrimary,
                          fontSize: MySize.fontSize(14),
                        ),
                        items: controller.categories.map((category) {
                          return DropdownMenuItem<String>(
                            value: category,
                            child: AppText(
                              text: category,
                              fontSize: 14,
                              color: AppColorConstants.textPrimary,
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            controller.setSelectedCategory(value);
                          }
                        },
                      ),
                    ),
                  ),
                  Space.height(16),

                  // Description Field (Optional)
                  const AppText(
                    text: 'Description (Optional)',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColorConstants.textPrimary,
                  ),
                  Space.height(8),
                  AppTextFormField(
                    controller: controller.descriptionController,
                    hintText: 'Enter movie description',
                    maxLines: 3,
                  ),
                  Space.height(24),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: AppButton(
                          text: 'Cancel',
                          onPressed: () => Get.back(),
                          backgroundColor: AppColorConstants.cardColor,
                          textColor: AppColorConstants.textSecondary,
                        ),
                      ),
                      Space.width(12),
                      Expanded(
                        child: AppButton(
                          text:
                              controller.isLoading ? 'Adding...' : 'Add Movie',
                          onPressed: controller.isLoading
                              ? null
                              : controller.addMovieToPlaylist,
                          backgroundColor: AppColorConstants.primaryColor,
                          textColor: AppColorConstants.textPrimary,
                          isLoading: controller.isLoading,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
