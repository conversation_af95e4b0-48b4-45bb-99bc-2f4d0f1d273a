import 'package:my_video/app_imports.dart';

class MainNavigationController extends GetxController {
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  final List<Widget> _pages = [
    const HomePage(),
    const MyPlaylistPage(),
    const SubscriptionPage(),
    const SettingsPage(),
  ];

  List<Widget> get pages => _pages;

  void changeTab(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      update();
    }
  }

  void showAddMovieDialog() {
    Get.dialog(
      const AddMovieDialog(),
      barrierDismissible: true,
    );
  }

  @override
  void onInit() {
    super.onInit();
    // Initialize any required data
  }

  @override
  void onClose() {
    super.onClose();
  }
}

// Home Page Implementation
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const AppText(
              text: 'MyVideo',
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            backgroundColor: AppColorConstants.backgroundColor,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  // TODO: Implement search
                },
              ),
            ],
          ),
          body: controller.isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: controller.refreshData,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Featured Movies Banner
                        if (controller.featuredMovies.isNotEmpty) ...[
                          _buildFeaturedBanner(controller),
                          Space.height(24),
                        ],

                        // Category Carousels
                        ...controller.categories.map((category) {
                          final movies =
                              controller.getMoviesByCategory(category.name);
                          if (movies.isEmpty) return const SizedBox.shrink();

                          return Column(
                            children: [
                              _buildCategorySection(
                                  category, movies, controller),
                              Space.height(24),
                            ],
                          );
                        }),

                        // Bottom padding
                        Space.height(100),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildFeaturedBanner(HomeController controller) {
    return SizedBox(
      height: MySize.height(220),
      child: CarouselSlider.builder(
        itemCount: controller.featuredMovies.length,
        itemBuilder: (context, index, realIndex) {
          final movie = controller.featuredMovies[index];
          return BannerMovieCard(
            movie: movie,
            onTap: () => controller.playMovie(movie),
          );
        },
        options: CarouselOptions(
          height: MySize.height(220),
          autoPlay: true,
          autoPlayInterval: const Duration(seconds: 5),
          enlargeCenterPage: true,
          viewportFraction: 0.9,
          onPageChanged: (index, reason) {
            // TODO: Update banner indicator if needed
          },
        ),
      ),
    );
  }

  Widget _buildCategorySection(CategoryModel category, List<MovieModel> movies,
      HomeController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                text: category.name,
                fontSize: MySize.fontSize(18),
                fontWeight: FontWeight.bold,
                color: AppColorConstants.textPrimary,
              ),
              GestureDetector(
                onTap: () => controller.viewAllMovies(category),
                child: AppText(
                  text: 'See All',
                  fontSize: MySize.fontSize(14),
                  color: AppColorConstants.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        Space.height(12),
        SizedBox(
          height: MySize.height(240),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: MySize.width(16)),
            itemCount: movies.length + 2, // +2 for ad placeholders
            itemBuilder: (context, index) {
              // First item: Ad placeholder
              if (index == 0) {
                return _buildAdPlaceholder();
              }

              // Last item: Ad placeholder
              if (index == movies.length + 1) {
                return _buildAdPlaceholder();
              }

              // Movie items
              final movieIndex = index - 1;
              final movie = movies[movieIndex];
              return MovieCard(
                movie: movie,
                onTap: () => controller.playMovie(movie),
                showTitle: true,
                showRating: true,
                showDuration: true,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAdPlaceholder() {
    return Container(
      width: MySize.width(140),
      height: MySize.height(200),
      margin: EdgeInsets.only(right: MySize.width(12)),
      decoration: BoxDecoration(
        color: AppColorConstants.cardColor,
        borderRadius: BorderRadius.circular(MySize.radius(12)),
        border: Border.all(
          color: AppColorConstants.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.ads_click_outlined,
            size: MySize.height(40),
            color: AppColorConstants.textHint,
          ),
          Space.height(8),
          AppText(
            text: 'Advertisement',
            fontSize: MySize.fontSize(12),
            color: AppColorConstants.textHint,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class MyPlaylistPage extends StatelessWidget {
  const MyPlaylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'My Playlist',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: AppText(
          text: 'My Playlist Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class SubscriptionPage extends StatelessWidget {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Subscription',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: AppText(
          text: 'Subscription Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Settings',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: AppText(
          text: 'Settings Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class AddMovieDialog extends StatelessWidget {
  const AddMovieDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(MySize.radius(16)),
      ),
      child: Container(
        padding: EdgeInsets.all(MySize.height(24)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppText(
              text: 'Add Movie to Playlist',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            Space.height(16),
            const AppText(
              text: 'Add Movie Dialog - Coming Soon',
              fontSize: 14,
              color: AppColorConstants.textSecondary,
            ),
            Space.height(24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const AppText(
                    text: 'Cancel',
                    color: AppColorConstants.textSecondary,
                  ),
                ),
                Space.width(8),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement add movie functionality
                    Get.back();
                  },
                  child: const AppText(
                    text: 'Add',
                    color: AppColorConstants.textPrimary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
