import 'package:my_video/app_imports.dart';

class MainNavigationController extends GetxController {
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  final List<Widget> _pages = [
    const HomePage(),
    const MyPlaylistPage(),
    const SubscriptionPage(),
    const SettingsPage(),
  ];

  List<Widget> get pages => _pages;

  void changeTab(int index) {
    if (_currentIndex != index) {
      _currentIndex = index;
      update();
    }
  }

  void showAddMovieDialog() {
    Get.dialog(
      const AddMovieDialog(),
      barrierDismissible: true,
    );
  }

  @override
  void onInit() {
    super.onInit();
    // Initialize any required data
  }

  @override
  void onClose() {
    super.onClose();
  }
}

// Placeholder pages - will be implemented in subsequent tasks
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'MyVideo',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search
            },
          ),
        ],
      ),
      body: const Center(
        child: AppText(
          text: 'Home Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class MyPlaylistPage extends StatelessWidget {
  const MyPlaylistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'My Playlist',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: AppText(
          text: 'My Playlist Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class SubscriptionPage extends StatelessWidget {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Subscription',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: AppText(
          text: 'Subscription Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const AppText(
          text: 'Settings',
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColorConstants.textPrimary,
        ),
        backgroundColor: AppColorConstants.backgroundColor,
        elevation: 0,
      ),
      body: const Center(
        child: AppText(
          text: 'Settings Page - Coming Soon',
          fontSize: 18,
          color: AppColorConstants.textPrimary,
        ),
      ),
    );
  }
}

class AddMovieDialog extends StatelessWidget {
  const AddMovieDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(MySize.radius(16)),
      ),
      child: Container(
        padding: EdgeInsets.all(MySize.height(24)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppText(
              text: 'Add Movie to Playlist',
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.textPrimary,
            ),
            Space.height(16),
            const AppText(
              text: 'Add Movie Dialog - Coming Soon',
              fontSize: 14,
              color: AppColorConstants.textSecondary,
            ),
            Space.height(24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const AppText(
                    text: 'Cancel',
                    color: AppColorConstants.textSecondary,
                  ),
                ),
                Space.width(8),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement add movie functionality
                    Get.back();
                  },
                  child: const AppText(
                    text: 'Add',
                    color: AppColorConstants.textPrimary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
