import 'package:my_video/app_imports.dart';

class AddMovieController extends GetxController {
  final Logger _logger = Logger();
  
  final TextEditingController urlController = TextEditingController();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  
  String _selectedCategory = 'Action';
  String get selectedCategory => _selectedCategory;
  
  List<String> _categories = ['Action', 'Drama', 'Comedy', 'Thriller', 'Romance'];
  List<String> get categories => _categories;
  
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  
  bool _isValidUrl = false;
  bool get isValidUrl => _isValidUrl;

  @override
  void onInit() {
    super.onInit();
    _loadCategories();
    urlController.addListener(_validateUrl);
  }

  void _loadCategories() {
    try {
      final categoryModels = HiveHelper.getAllCategories();
      if (categoryModels.isNotEmpty) {
        _categories = categoryModels.map((c) => c.name).toList();
        _selectedCategory = _categories.first;
        update();
      }
    } catch (e) {
      _logger.e('Error loading categories: $e');
    }
  }

  void _validateUrl() {
    final url = urlController.text.trim();
    _isValidUrl = _isYouTubeUrl(url);
    update();
  }

  bool _isYouTubeUrl(String url) {
    if (url.isEmpty) return false;
    
    final RegExp youtubeRegex = RegExp(
      r'^(https?://)?(www\.)?(youtube\.com/(watch\?v=|embed/)|youtu\.be/)[\w-]+',
      caseSensitive: false,
    );
    
    return youtubeRegex.hasMatch(url);
  }

  String? _extractVideoId(String url) {
    final RegExp regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  void setSelectedCategory(String category) {
    _selectedCategory = category;
    update();
  }

  Future<void> addMovieToPlaylist() async {
    if (!_isValidUrl) {
      _showError('Please enter a valid YouTube URL');
      return;
    }

    if (titleController.text.trim().isEmpty) {
      _showError('Please enter a movie title');
      return;
    }

    _setLoading(true);
    
    try {
      final videoId = _extractVideoId(urlController.text.trim());
      if (videoId == null) {
        _showError('Invalid YouTube URL');
        return;
      }

      // Create movie model
      final movie = MovieModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: titleController.text.trim(),
        description: descriptionController.text.trim().isEmpty 
            ? null 
            : descriptionController.text.trim(),
        youtubeUrl: urlController.text.trim(),
        thumbnailUrl: 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg',
        category: _selectedCategory,
        duration: null,
        rating: null,
        releaseYear: DateTime.now().year,
        genre: [_selectedCategory],
        isFeatured: false,
        viewCount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save movie to database
      await HiveHelper.saveMovie(movie);
      
      // Add to default playlist
      await HiveHelper.addMovieToDefaultPlaylist(movie.id);
      
      _showSuccess('Movie added to playlist successfully!');
      _clearForm();
      Get.back();
      
      _logger.i('Added movie to playlist: ${movie.title}');
    } catch (e) {
      _logger.e('Error adding movie to playlist: $e');
      _showError('Failed to add movie. Please try again.');
    } finally {
      _setLoading(false);
    }
  }

  void _clearForm() {
    urlController.clear();
    titleController.clear();
    descriptionController.clear();
    _selectedCategory = _categories.first;
    _isValidUrl = false;
    update();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    update();
  }

  void _showSuccess(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorGreen,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  void _showError(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColorConstants.colorRed,
      colorText: AppColorConstants.textPrimary,
      duration: const Duration(seconds: 3),
    );
  }

  @override
  void onClose() {
    urlController.dispose();
    titleController.dispose();
    descriptionController.dispose();
    super.onClose();
  }
}
