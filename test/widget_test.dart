// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:my_video/app_imports.dart';

void main() {
  group('App Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Verify that the app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Splash screen should be displayed initially',
        (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Wait for the initial frame
      await tester.pump();

      // The app should show some content (could be splash screen or login)
      expect(find.byType(Scaffold), findsOneWidget);
    });
  });

  group('Helper Tests', () {
    test('Email validation should work correctly', () {
      // Test valid emails
      expect(ValidationHelper.isValidEmail('<EMAIL>'), true);
      expect(ValidationHelper.isValidEmail('<EMAIL>'), true);

      // Test invalid emails
      expect(ValidationHelper.isValidEmail('invalid-email'), false);
      expect(ValidationHelper.isValidEmail('test@'), false);
      expect(ValidationHelper.isValidEmail('@example.com'), false);
      expect(ValidationHelper.isValidEmail(''), false);
    });

    test('Phone validation should work correctly', () {
      // Test valid phone numbers
      expect(ValidationHelper.isValidPhone('1234567890'), true);
      expect(ValidationHelper.isValidPhone('9876543210'), true);

      // Test invalid phone numbers
      expect(ValidationHelper.isValidPhone('123456789'), false); // Too short
      expect(ValidationHelper.isValidPhone('12345678901'), false); // Too long
      expect(ValidationHelper.isValidPhone('abcdefghij'), false); // Not numeric
      expect(ValidationHelper.isValidPhone(''), false); // Empty
    });
  });

  group('Model Tests', () {
    test('UserModel should serialize and deserialize correctly', () {
      final userData = {
        'id': '1',
        'name': 'Test User',
        'email': '<EMAIL>',
        'phone': '1234567890',
        'user_type': 'user',
        'created_at': '2023-01-01T00:00:00.000Z',
        'updated_at': '2023-01-01T00:00:00.000Z',
        'is_active': true,
        'is_verified': false,
      };

      final user = UserModel.fromJson(userData);

      expect(user.id, '1');
      expect(user.name, 'Test User');
      expect(user.email, '<EMAIL>');
      expect(user.phone, '1234567890');
      expect(user.userType, UserType.user);
      expect(user.isActive, true);
      expect(user.isVerified, false);

      final json = user.toJson();
      expect(json['id'], '1');
      expect(json['name'], 'Test User');
      expect(json['email'], '<EMAIL>');
    });
  });
}
