#!/bin/bash

# Flutter Project Template Setup Script
# This script helps you customize the template for your new project

echo "🚀 Flutter Project Template Setup"
echo "=================================="

# Get project details from user
read -p "Enter your project name (e.g., my_awesome_app): " PROJECT_NAME
read -p "Enter your app display name (e.g., My Awesome App): " APP_DISPLAY_NAME
read -p "Enter your package name (e.g., com.company.myapp): " PACKAGE_NAME
read -p "Enter your organization name (e.g., com.company): " ORG_NAME

# Validate inputs
if [ -z "$PROJECT_NAME" ] || [ -z "$APP_DISPLAY_NAME" ] || [ -z "$PACKAGE_NAME" ] || [ -z "$ORG_NAME" ]; then
    echo "❌ All fields are required!"
    exit 1
fi

echo ""
echo "📝 Project Configuration:"
echo "Project Name: $PROJECT_NAME"
echo "App Display Name: $APP_DISPLAY_NAME"
echo "Package Name: $PACKAGE_NAME"
echo "Organization: $ORG_NAME"
echo ""

read -p "Is this correct? (y/n): " CONFIRM
if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
    echo "❌ Setup cancelled"
    exit 1
fi

echo ""
echo "🔧 Updating project files..."

# Update pubspec.yaml
sed -i.bak "s/name: new_project_template/name: $PROJECT_NAME/g" pubspec.yaml
sed -i.bak "s/description: \"A new Flutter project template with organized structure.\"/description: \"$APP_DISPLAY_NAME - Flutter Application\"/g" pubspec.yaml

# Update main.dart imports
sed -i.bak "s/package:new_project_template/package:$PROJECT_NAME/g" lib/main.dart
sed -i.bak "s/package:new_project_template/package:$PROJECT_NAME/g" lib/main_dev.dart

# Update app_imports.dart
sed -i.bak "s/package:new_project_template/package:$PROJECT_NAME/g" lib/app_imports.dart

# Update test files
sed -i.bak "s/package:new_project_template/package:$PROJECT_NAME/g" test/widget_test.dart

# Update app constants
sed -i.bak "s/New Project Template/$APP_DISPLAY_NAME/g" lib/app/constants/app_constants.dart
sed -i.bak "s/New Project Template Dev/$APP_DISPLAY_NAME Dev/g" lib/app/constants/app_constants.dart

# Update Android configuration
sed -i.bak "s/com.example.new_project_template/$PACKAGE_NAME/g" android/app/build.gradle
sed -i.bak "s/com.example.new_project_template/$PACKAGE_NAME/g" android/app/src/main/AndroidManifest.xml
sed -i.bak "s/New Project Template/$APP_DISPLAY_NAME/g" android/app/src/main/AndroidManifest.xml

# Update Android MainActivity
mkdir -p "android/app/src/main/kotlin/$(echo $PACKAGE_NAME | tr '.' '/')"
sed "s/com.example.new_project_template/$PACKAGE_NAME/g" android/app/src/main/kotlin/com/example/new_project_template/MainActivity.kt > "android/app/src/main/kotlin/$(echo $PACKAGE_NAME | tr '.' '/')/MainActivity.kt"

# Update iOS configuration
sed -i.bak "s/New Project Template/$APP_DISPLAY_NAME/g" ios/Runner/Info.plist
sed -i.bak "s/new_project_template/$PROJECT_NAME/g" ios/Runner/Info.plist

# Update web configuration
sed -i.bak "s/New Project Template/$APP_DISPLAY_NAME/g" web/index.html
sed -i.bak "s/New Project Template/$APP_DISPLAY_NAME/g" web/manifest.json
sed -i.bak "s/A new Flutter project template/$APP_DISPLAY_NAME - Flutter Application/g" web/index.html
sed -i.bak "s/A new Flutter project template/$APP_DISPLAY_NAME - Flutter Application/g" web/manifest.json

# Update README
sed -i.bak "s/Flutter Project Template/$APP_DISPLAY_NAME/g" README.md
sed -i.bak "s/new_flutter_project_template/$PROJECT_NAME/g" README.md

# Clean up backup files
find . -name "*.bak" -delete

# Remove old Android directory structure
rm -rf android/app/src/main/kotlin/com/example/new_project_template

echo ""
echo "✅ Project setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Run 'flutter pub get' to install dependencies"
echo "2. Update your API endpoints in lib/app/config/app_config.dart"
echo "3. Add your app icons and assets"
echo "4. Update app colors and themes in lib/app/constants/app_constants.dart"
echo "5. Run 'flutter run' to test your app"
echo ""
echo "🎉 Happy coding!"
