# Animation Directory

Place your animation files here.

## Recommended Animation Files

- loading.json - Loading animation (<PERSON><PERSON>)
- success.json - Success animation (<PERSON><PERSON>)
- error.json - Error animation (<PERSON><PERSON>)
- empty_state.json - Empty state animation (<PERSON><PERSON>)
- splash_animation.json - Splash screen animation (<PERSON><PERSON>)

## File Formats

- **JSON**: Lottie animation files (preferred)
- **GIF**: For simple animations
- **WebP**: Animated WebP files

## Lottie Animations

Download from:
- [LottieFiles](https://lottiefiles.com/)
- [Rive](https://rive.app/)

## Usage

```dart
// Lottie animations
Lottie.asset('assets/animation/loading.json')

// With controller
Lottie.asset(
  'assets/animation/success.json',
  controller: _animationController,
  onLoaded: (composition) {
    _animationController.duration = composition.duration;
    _animationController.forward();
  },
)

// GIF animations
Image.asset('assets/animation/loading.gif')
```

## Optimization

- Keep animation files small (< 100KB when possible)
- Use appropriate frame rates (24-30 fps)
- Optimize for mobile performance
- Test on different devices
