# Data Directory

Place your static data files here.

## Recommended Data Files

- countries.json - List of countries
- states.json - List of states/provinces
- cities.json - List of cities
- categories.json - App categories
- mock_data.json - Mock data for development
- config.json - App configuration data

## File Formats

- **JSON**: For structured data
- **CSV**: For tabular data
- **XML**: For configuration files
- **TXT**: For simple text data

## Usage

```dart
// Loading JSON data
Future<Map<String, dynamic>> loadJsonData(String fileName) async {
  final String jsonString = await rootBundle.loadString('assets/data/$fileName');
  return json.decode(jsonString);
}

// Example usage
final countries = await loadJsonData('countries.json');

// Using in FutureBuilder
FutureBuilder<Map<String, dynamic>>(
  future: loadJsonData('config.json'),
  builder: (context, snapshot) {
    if (snapshot.hasData) {
      return Text(snapshot.data!['app_name']);
    }
    return CircularProgressIndicator();
  },
)
```

## Best Practices

- Keep data files small and optimized
- Use appropriate data structures
- Validate data before using
- Consider using local database for large datasets
- Update data files as needed during app updates
