# Images Directory

Place your image files here.

## Recommended Image Files

- logo.png - App logo
- placeholder.png - Placeholder image
- no_data.png - No data illustration
- no_internet.png - No internet illustration
- splash_bg.png - Splash screen background
- onboarding_1.png - Onboarding screen 1
- onboarding_2.png - Onboarding screen 2
- onboarding_3.png - Onboarding screen 3

## File Formats

- **PNG**: For images with transparency
- **JPG/JPEG**: For photos and complex images
- **WebP**: For smaller file sizes (if supported)

## Screen Densities

Create multiple versions for different screen densities:
- `image.png` (1x)
- `2.0x/image.png` (2x)
- `3.0x/image.png` (3x)

## Naming Convention

Use lowercase with underscores:
- `app_logo.png`
- `user_avatar_placeholder.png`
- `background_pattern.jpg`

## Usage

```dart
// Basic usage
Image.asset('assets/images/logo.png')

// With error handling
Image.asset(
  'assets/images/logo.png',
  errorBuilder: (context, error, stackTrace) {
    return Icon(Icons.error);
  },
)

// Using constants
Image.asset(AppAssetsConstants.imageLogo)
```
