name: my_video
description: "A Flutter video application with organized structure."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  shimmer: ^3.0.0
  lottie: ^3.1.2
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.10+1
  get: ^4.6.6
  get_it: ^7.7.0
  intl: ^0.19.0
  go_router: ^13.2.1
  logger: ^2.2.0
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.0
  dropdown_button2: ^2.3.9
  bot_toast: ^4.1.3
  file_picker: ^8.0.5
  image_picker: ^1.1.2
  internet_connection_checker: ^1.0.0+1
  url_launcher: ^6.3.0
  http: ^1.2.1
  shared_preferences: ^2.2.3 # Manage catch storage
  json_annotation: ^4.9.0 # Generate JSON
  external_path: ^1.0.3 # Manage External Storage
  path_provider: ^2.1.3 # Get Path
  flutter_html: ^3.0.0
  google_fonts: ^6.2.1
  translator: ^1.0.3+1
  html: ^0.15.6

  # Movie Streaming App Dependencies
  youtube_player_iframe: ^5.2.1
  google_mobile_ads: ^5.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_inappwebview: ^6.0.0
  share_plus: ^7.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  build_runner: ^2.4.11
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/icons/
    - assets/images/
    - assets/animation/
    - assets/data/
