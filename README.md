# Flutter Project Template

A comprehensive Flutter project template with organized structure, best practices, and essential features pre-configured.

## 🚀 Features

- **Clean Architecture**: Well-organized folder structure following Flutter best practices
- **State Management**: GetX for state management, navigation, and dependency injection
- **API Integration**: HTTP client with error handling and connectivity checks
- **Local Storage**: SharedPreferences wrapper for data persistence
- **Routing**: Go Router for declarative navigation
- **UI Components**: Reusable custom widgets and components
- **Localization**: Multi-language support with GetX translations
- **Form Validation**: Comprehensive validation helpers
- **File Handling**: File picker, image picker, and download utilities
- **Permissions**: Permission handling utilities
- **Animations**: Lottie animations and custom transitions
- **Error Handling**: Comprehensive error handling and logging
- **Network Connectivity**: Internet connection monitoring
- **Toast Messages**: User-friendly toast notifications
- **Loading States**: Loading indicators and shimmer effects

## 📁 Project Structure

```
lib/
├── app/                          # Core app configuration
│   ├── cache/                    # Local storage utilities
│   ├── config/                   # App configuration
│   ├── constants/                # App constants
│   ├── extensions/               # Dart extensions
│   ├── helper/                   # Utility helpers
│   ├── routes/                   # App routing
│   └── ui/                       # Reusable UI components
├── controllers/                  # GetX controllers
├── enum/                         # Enumerations
├── localization/                 # Internationalization
├── model/                        # Data models
├── pages/                        # App screens/pages
├── repository/                   # Data repositories
├── serializable/                 # JSON serializable models
├── app_imports.dart             # Centralized imports
└── main.dart                    # App entry point
```

## 🛠 Setup Instructions

### Prerequisites

- Flutter SDK (>=3.4.3)
- Dart SDK
- Android Studio / VS Code
- Git

### Installation

1. **Clone or download this template**
   ```bash
   git clone <repository-url>
   cd new_flutter_project_template
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code (if needed)**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Configuration

1. **Update app name and package**
   - Update `name` in `pubspec.yaml`
   - Update package name in Android and iOS configurations
   - Update `AppStringConstants` in `lib/app/constants/app_constants.dart`

2. **Configure API endpoints**
   - Update base URLs in `lib/app/config/app_config.dart`

3. **Add assets**
   - Place fonts in `assets/fonts/`
   - Place images in `assets/images/`
   - Place icons in `assets/icons/`
   - Place animations in `assets/animation/`

## 📦 Dependencies

### Core Dependencies
- **flutter**: Flutter SDK
- **get**: State management, navigation, and dependency injection
- **get_it**: Service locator for dependency injection
- **go_router**: Declarative routing
- **http**: HTTP client for API calls
- **shared_preferences**: Local data storage

### UI Dependencies
- **cached_network_image**: Network image caching
- **flutter_svg**: SVG image support
- **lottie**: Lottie animations
- **shimmer**: Shimmer loading effects
- **bot_toast**: Toast notifications

### Utility Dependencies
- **intl**: Internationalization and date formatting
- **logger**: Logging utility
- **permission_handler**: Permission management
- **device_info_plus**: Device information
- **file_picker**: File selection
- **image_picker**: Image selection
- **url_launcher**: URL launching
- **internet_connection_checker**: Network connectivity
- **path_provider**: File system paths
- **external_path**: External storage paths

### Development Dependencies
- **flutter_lints**: Linting rules
- **build_runner**: Code generation
- **json_serializable**: JSON serialization
- **json_annotation**: JSON annotations

## 🎨 Customization

### Colors and Themes
Update colors in `lib/app/constants/app_constants.dart`:
```dart
class AppColorConstants {
  static const Color colorPrimary = Color(0xFF2196F3);
  // Add your custom colors
}
```

### Fonts
1. Add font files to `assets/fonts/`
2. Update `pubspec.yaml` fonts section
3. Update `AppAssetsConstants.defaultFont`

### API Configuration
Update base URLs in `lib/app/config/app_config.dart`:
```dart
String get baseUrl {
  switch (flavor) {
    case Flavor.dev:
      return 'https://your-dev-api.com/';
    case Flavor.prod:
      return 'https://your-prod-api.com/';
  }
}
```

## 🌐 Localization

Add translations in `lib/localization/app_localization.dart`:
```dart
'en_US': {
  'welcome': 'Welcome',
  'login': 'Login',
  // Add more translations
},
'es_ES': {
  'welcome': 'Bienvenido',
  'login': 'Iniciar sesión',
  // Add Spanish translations
},
```

Usage in widgets:
```dart
Text('welcome'.tr)
```

## 🧪 Testing

Run tests:
```bash
flutter test
```

## 📱 Building

### Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🎯 Next Steps

After setting up the template:

1. **Customize the app** according to your requirements
2. **Add your features** using the established patterns
3. **Update the README** with your project-specific information
4. **Set up CI/CD** for automated testing and deployment
5. **Add more tests** to ensure code quality
6. **Configure analytics** and crash reporting
7. **Set up app store listings** and metadata

Happy coding! 🚀
